// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 331C807B294A618700263BE5 /* RunnerTests.swift */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		436CC09DD87E5075024EE689 /* Pods_RunnerTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C165E8FA18B4F57B9A908874 /* Pods_RunnerTests.framework */; };
		5B89DDDB2CC26A5B00C4FAD1 /* YTXMonitor.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5B89DDD82CC26A5B00C4FAD1 /* YTXMonitor.framework */; };
		5B89DDDC2CC26A5B00C4FAD1 /* YTXOperators.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5B89DDD92CC26A5B00C4FAD1 /* YTXOperators.framework */; };
		5B89DDDD2CC26A5B00C4FAD1 /* ATAuthSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5B89DDDA2CC26A5B00C4FAD1 /* ATAuthSDK.framework */; };
		5BECC4AF2CBF977F008B550C /* Network.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5BECC4AE2CBF977F008B550C /* Network.framework */; };
		5BFCA9F92CB8C7B00003D8B0 /* AliAuthHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 5BFCA9F82CB8C7B00003D8B0 /* AliAuthHandler.m */; };
		5BFEDC3D2CC642C1005FEF9C /* ATAuthSDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 5BFEDC3C2CC642C1005FEF9C /* ATAuthSDK.bundle */; };
		727D48C5DF6C909472D947CB /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = EEA0CB080708958E8A517A59 /* Pods_Runner.framework */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		FBACF8A02E78059C000A2D41 /* UIImage+OpenCV.mm in Sources */ = {isa = PBXBuildFile; fileRef = FBACF89C2E78059C000A2D41 /* UIImage+OpenCV.mm */; };
		FBACF8A12E78059C000A2D41 /* beginViedoCompareViewController.mm in Sources */ = {isa = PBXBuildFile; fileRef = FBACF8942E78059C000A2D41 /* beginViedoCompareViewController.mm */; };
		FBACF8A22E78059C000A2D41 /* FFVideoCapturer.m in Sources */ = {isa = PBXBuildFile; fileRef = FBACF89A2E78059C000A2D41 /* FFVideoCapturer.m */; };
		FBACF8A32E78059C000A2D41 /* FaceRecognizerManagers.mm in Sources */ = {isa = PBXBuildFile; fileRef = FBACF8982E78059C000A2D41 /* FaceRecognizerManagers.mm */; };
		FBACF8A52E78059C000A2D41 /* CSRImageViewController.mm in Sources */ = {isa = PBXBuildFile; fileRef = FBACF8962E78059C000A2D41 /* CSRImageViewController.mm */; };
		FBACF8AF2E7805CB000A2D41 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8AE2E7805CB000A2D41 /* Accelerate.framework */; };
		FBACF8B02E7805D3000A2D41 /* libc++abi.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8AD2E7805BE000A2D41 /* libc++abi.tbd */; };
		FBACF8C62E780982000A2D41 /* SeetaFaceRecognizer610.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C32E7808DD000A2D41 /* SeetaFaceRecognizer610.framework */; };
		FBACF8C82E780A08000A2D41 /* SeetaFaceLandmarker600.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C22E7808DD000A2D41 /* SeetaFaceLandmarker600.framework */; };
		FBACF8CB2E780AA8000A2D41 /* opencv2.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8CA2E780AA8000A2D41 /* opencv2.framework */; };
		FBACF8CD2E780AD8000A2D41 /* tennis.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C42E7808DD000A2D41 /* tennis.framework */; };
		FBACF8CE2E780AF6000A2D41 /* libjpeg.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8BE2E7808DD000A2D41 /* libjpeg.a */; };
		FBACF8CF2E780B35000A2D41 /* SeetaAuthorize.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C02E7808DD000A2D41 /* SeetaAuthorize.framework */; };
		FBACF8D12E780BBA000A2D41 /* SeetaFaceDetector600.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C12E7808DD000A2D41 /* SeetaFaceDetector600.framework */; };
		FBACF8F12E780E00000A2D41 /* SeetaAuthorize.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C02E7808DD000A2D41 /* SeetaAuthorize.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		FBACF8F22E780E00000A2D41 /* SeetaFaceDetector600.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C12E7808DD000A2D41 /* SeetaFaceDetector600.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		FBACF8F32E780E00000A2D41 /* SeetaFaceLandmarker600.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C22E7808DD000A2D41 /* SeetaFaceLandmarker600.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		FBACF8F42E780E00000A2D41 /* SeetaFaceRecognizer610.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C32E7808DD000A2D41 /* SeetaFaceRecognizer610.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		FBACF8F62E780E00000A2D41 /* tennis.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = FBACF8C42E7808DD000A2D41 /* tennis.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		FBACF94B2E78110F000A2D41 /* assert in Resources */ = {isa = PBXBuildFile; fileRef = FBACF94A2E78110F000A2D41 /* assert */; };
		FE1BCFDB2CF30FF000EAEF21 /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = FE1BCFDA2CF30FE000EAEF21 /* libsqlite3.tbd */; };
		FE6CFE2B2DA4C79E001F8DAC /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FE6CFE292DA4C79E001F8DAC /* Main.storyboard */; };
		FE6CFE2C2DA4C79E001F8DAC /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = FE6CFE272DA4C79E001F8DAC /* LaunchScreen.storyboard */; };
		FEA12BF42E77E66C003AA7D2 /* SeetaFacePlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = FEA12BF32E77E66C003AA7D2 /* SeetaFacePlugin.m */; };
		FEB2294A2CFCB6E200F9DAA8 /* HealthKitPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = FEB229492CFCB6E200F9DAA8 /* HealthKitPlugin.swift */; };
		FEF8E6D42D54987B0006FF1A /* UMCommonSwift.swift in Sources */ = {isa = PBXBuildFile; fileRef = FEF8E6D12D54987B0006FF1A /* UMCommonSwift.swift */; };
		FEF8E6D52D54987B0006FF1A /* UMAnalyticsSwift.swift in Sources */ = {isa = PBXBuildFile; fileRef = FEF8E6CF2D54987B0006FF1A /* UMAnalyticsSwift.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		331C8085294A63A400263BE5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97C146ED1CF9000F007C117D;
			remoteInfo = Runner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		FBACF8F02E780E00000A2D41 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				FBACF8F12E780E00000A2D41 /* SeetaAuthorize.framework in Embed Frameworks */,
				FBACF8F22E780E00000A2D41 /* SeetaFaceDetector600.framework in Embed Frameworks */,
				FBACF8F32E780E00000A2D41 /* SeetaFaceLandmarker600.framework in Embed Frameworks */,
				FBACF8F42E780E00000A2D41 /* SeetaFaceRecognizer610.framework in Embed Frameworks */,
				FBACF8F62E780E00000A2D41 /* tennis.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		331C807B294A618700263BE5 /* RunnerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunnerTests.swift; sourceTree = "<group>"; };
		331C8081294A63A400263BE5 /* RunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3E9C9075A1F7D2CC660AB711 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		5B89DDD82CC26A5B00C4FAD1 /* YTXMonitor.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTXMonitor.framework; sourceTree = "<group>"; };
		5B89DDD92CC26A5B00C4FAD1 /* YTXOperators.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTXOperators.framework; sourceTree = "<group>"; };
		5B89DDDA2CC26A5B00C4FAD1 /* ATAuthSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ATAuthSDK.framework; sourceTree = "<group>"; };
		5BECC4AE2CBF977F008B550C /* Network.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Network.framework; path = System/Library/Frameworks/Network.framework; sourceTree = SDKROOT; };
		5BFCA9F72CB8C7B00003D8B0 /* AliAuthHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AliAuthHandler.h; sourceTree = "<group>"; };
		5BFCA9F82CB8C7B00003D8B0 /* AliAuthHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AliAuthHandler.m; sourceTree = "<group>"; };
		5BFCA9FA2CBA20A50003D8B0 /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		5BFEDC3C2CC642C1005FEF9C /* ATAuthSDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; name = ATAuthSDK.bundle; path = ATAuthSDK.framework/ATAuthSDK.bundle; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		80A5885CA7173881FDA62581 /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		877E6798EEB4B69530AB10FE /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9C74C2DC8F0DC6E66390AA06 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		C165E8FA18B4F57B9A908874 /* Pods_RunnerTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		CF4A27BFDEE7FD35AF16F5EA /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		EEA0CB080708958E8A517A59 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F1108AB59AD4006870CA27D5 /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		FBACF8932E78059C000A2D41 /* beginViedoCompareViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = beginViedoCompareViewController.h; sourceTree = "<group>"; };
		FBACF8942E78059C000A2D41 /* beginViedoCompareViewController.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = beginViedoCompareViewController.mm; sourceTree = "<group>"; };
		FBACF8952E78059C000A2D41 /* CSRImageViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CSRImageViewController.h; sourceTree = "<group>"; };
		FBACF8962E78059C000A2D41 /* CSRImageViewController.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = CSRImageViewController.mm; sourceTree = "<group>"; };
		FBACF8972E78059C000A2D41 /* FaceRecognizerManagers.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FaceRecognizerManagers.h; sourceTree = "<group>"; };
		FBACF8982E78059C000A2D41 /* FaceRecognizerManagers.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = FaceRecognizerManagers.mm; sourceTree = "<group>"; };
		FBACF8992E78059C000A2D41 /* FFVideoCapturer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FFVideoCapturer.h; sourceTree = "<group>"; };
		FBACF89A2E78059C000A2D41 /* FFVideoCapturer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FFVideoCapturer.m; sourceTree = "<group>"; };
		FBACF89B2E78059C000A2D41 /* UIImage+OpenCV.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+OpenCV.h"; sourceTree = "<group>"; };
		FBACF89C2E78059C000A2D41 /* UIImage+OpenCV.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = "UIImage+OpenCV.mm"; sourceTree = "<group>"; };
		FBACF8AD2E7805BE000A2D41 /* libc++abi.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++abi.tbd"; path = "usr/lib/libc++abi.tbd"; sourceTree = SDKROOT; };
		FBACF8AE2E7805CB000A2D41 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		FBACF8BE2E7808DD000A2D41 /* libjpeg.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libjpeg.a; sourceTree = "<group>"; };
		FBACF8C02E7808DD000A2D41 /* SeetaAuthorize.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = SeetaAuthorize.framework; sourceTree = "<group>"; };
		FBACF8C12E7808DD000A2D41 /* SeetaFaceDetector600.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = SeetaFaceDetector600.framework; sourceTree = "<group>"; };
		FBACF8C22E7808DD000A2D41 /* SeetaFaceLandmarker600.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = SeetaFaceLandmarker600.framework; sourceTree = "<group>"; };
		FBACF8C32E7808DD000A2D41 /* SeetaFaceRecognizer610.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = SeetaFaceRecognizer610.framework; sourceTree = "<group>"; };
		FBACF8C42E7808DD000A2D41 /* tennis.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = tennis.framework; sourceTree = "<group>"; };
		FBACF8CA2E780AA8000A2D41 /* opencv2.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = opencv2.framework; path = Runner/SeetaFace/FrameWork/opencv2.framework; sourceTree = "<group>"; };
		FBACF94A2E78110F000A2D41 /* assert */ = {isa = PBXFileReference; lastKnownFileType = folder; path = assert; sourceTree = "<group>"; };
		FE1BCFDA2CF30FE000EAEF21 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		FE26E2302D2CD9EF0034345D /* RunnerDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerDebug.entitlements; sourceTree = "<group>"; };
		FE26E2312D2CDB270034345D /* RunnerRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RunnerRelease.entitlements; sourceTree = "<group>"; };
		FE6CFE262DA4C79E001F8DAC /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		FE6CFE282DA4C79E001F8DAC /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Main.storyboard; sourceTree = "<group>"; };
		FEA12BF22E77E66C003AA7D2 /* SeetaFacePlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SeetaFacePlugin.h; sourceTree = "<group>"; };
		FEA12BF32E77E66C003AA7D2 /* SeetaFacePlugin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SeetaFacePlugin.m; sourceTree = "<group>"; };
		FEB229492CFCB6E200F9DAA8 /* HealthKitPlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthKitPlugin.swift; sourceTree = "<group>"; };
		FEF8E6CF2D54987B0006FF1A /* UMAnalyticsSwift.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UMAnalyticsSwift.swift; sourceTree = "<group>"; };
		FEF8E6D12D54987B0006FF1A /* UMCommonSwift.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UMCommonSwift.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		FBACF9422E7810B6000A2D41 /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				model/face_detector.csta,
				model/face_landmarker_pts5.csta,
				model/face_recognizer_light.csta,
				model/face_recognizer.csta,
			);
			target = 97C146ED1CF9000F007C117D /* Runner */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		FBACF8852E78054A000A2D41 /* assert */ = {isa = PBXFileSystemSynchronizedRootGroup; exceptions = (FBACF9422E7810B6000A2D41 /* PBXFileSystemSynchronizedBuildFileExceptionSet */, ); explicitFileTypes = {}; explicitFolders = (); path = assert; sourceTree = "<group>"; };
		FBACF9492E7810FB000A2D41 /* assert */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = assert; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		8C1F908EB9CE85E2D9F9F5AC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				436CC09DD87E5075024EE689 /* Pods_RunnerTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				FBACF8D12E780BBA000A2D41 /* SeetaFaceDetector600.framework in Frameworks */,
				FBACF8CE2E780AF6000A2D41 /* libjpeg.a in Frameworks */,
				FBACF8C82E780A08000A2D41 /* SeetaFaceLandmarker600.framework in Frameworks */,
				FBACF8C62E780982000A2D41 /* SeetaFaceRecognizer610.framework in Frameworks */,
				FBACF8CD2E780AD8000A2D41 /* tennis.framework in Frameworks */,
				FBACF8CF2E780B35000A2D41 /* SeetaAuthorize.framework in Frameworks */,
				FBACF8CB2E780AA8000A2D41 /* opencv2.framework in Frameworks */,
				FE1BCFDB2CF30FF000EAEF21 /* libsqlite3.tbd in Frameworks */,
				FBACF8AF2E7805CB000A2D41 /* Accelerate.framework in Frameworks */,
				5B89DDDB2CC26A5B00C4FAD1 /* YTXMonitor.framework in Frameworks */,
				5B89DDDC2CC26A5B00C4FAD1 /* YTXOperators.framework in Frameworks */,
				5B89DDDD2CC26A5B00C4FAD1 /* ATAuthSDK.framework in Frameworks */,
				FBACF8B02E7805D3000A2D41 /* libc++abi.tbd in Frameworks */,
				727D48C5DF6C909472D947CB /* Pods_Runner.framework in Frameworks */,
				5BECC4AF2CBF977F008B550C /* Network.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		331C8082294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C807B294A618700263BE5 /* RunnerTests.swift */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		5856857DF26FBF59EFBF6860 /* Pods */ = {
			isa = PBXGroup;
			children = (
				9C74C2DC8F0DC6E66390AA06 /* Pods-Runner.debug.xcconfig */,
				CF4A27BFDEE7FD35AF16F5EA /* Pods-Runner.release.xcconfig */,
				3E9C9075A1F7D2CC660AB711 /* Pods-Runner.profile.xcconfig */,
				80A5885CA7173881FDA62581 /* Pods-RunnerTests.debug.xcconfig */,
				877E6798EEB4B69530AB10FE /* Pods-RunnerTests.release.xcconfig */,
				F1108AB59AD4006870CA27D5 /* Pods-RunnerTests.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		8D13BBCFD48F58EAB470A477 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				FBACF8CA2E780AA8000A2D41 /* opencv2.framework */,
				FBACF8C52E7808DD000A2D41 /* FrameWork */,
				FBACF8AE2E7805CB000A2D41 /* Accelerate.framework */,
				FBACF8AD2E7805BE000A2D41 /* libc++abi.tbd */,
				FE1BCFDA2CF30FE000EAEF21 /* libsqlite3.tbd */,
				5B89DDDA2CC26A5B00C4FAD1 /* ATAuthSDK.framework */,
				5B89DDD82CC26A5B00C4FAD1 /* YTXMonitor.framework */,
				5B89DDD92CC26A5B00C4FAD1 /* YTXOperators.framework */,
				5BECC4AE2CBF977F008B550C /* Network.framework */,
				EEA0CB080708958E8A517A59 /* Pods_Runner.framework */,
				C165E8FA18B4F57B9A908874 /* Pods_RunnerTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				FBACF8852E78054A000A2D41 /* assert */,
				FBACF9492E7810FB000A2D41 /* assert */,
				FBACF94A2E78110F000A2D41 /* assert */,
				5BFEDC3C2CC642C1005FEF9C /* ATAuthSDK.bundle */,
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				331C8082294A63A400263BE5 /* RunnerTests */,
				5856857DF26FBF59EFBF6860 /* Pods */,
				8D13BBCFD48F58EAB470A477 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				331C8081294A63A400263BE5 /* RunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				FBACF89F2E78059C000A2D41 /* SeetaFace */,
				FE6CFE2A2DA4C79E001F8DAC /* Base.lproj */,
				FEF8E6D32D54987B0006FF1A /* UMSwift */,
				FE26E2312D2CDB270034345D /* RunnerRelease.entitlements */,
				FE26E2302D2CD9EF0034345D /* RunnerDebug.entitlements */,
				5BFCA9FA2CBA20A50003D8B0 /* Runner.entitlements */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C147021CF9000F007C117D /* Info.plist */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				5BFCA9F72CB8C7B00003D8B0 /* AliAuthHandler.h */,
				5BFCA9F82CB8C7B00003D8B0 /* AliAuthHandler.m */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
				FEB229492CFCB6E200F9DAA8 /* HealthKitPlugin.swift */,
				FEA12BF22E77E66C003AA7D2 /* SeetaFacePlugin.h */,
				FEA12BF32E77E66C003AA7D2 /* SeetaFacePlugin.m */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		FBACF89F2E78059C000A2D41 /* SeetaFace */ = {
			isa = PBXGroup;
			children = (
				FBACF8932E78059C000A2D41 /* beginViedoCompareViewController.h */,
				FBACF8942E78059C000A2D41 /* beginViedoCompareViewController.mm */,
				FBACF8952E78059C000A2D41 /* CSRImageViewController.h */,
				FBACF8962E78059C000A2D41 /* CSRImageViewController.mm */,
				FBACF8972E78059C000A2D41 /* FaceRecognizerManagers.h */,
				FBACF8982E78059C000A2D41 /* FaceRecognizerManagers.mm */,
				FBACF8992E78059C000A2D41 /* FFVideoCapturer.h */,
				FBACF89A2E78059C000A2D41 /* FFVideoCapturer.m */,
				FBACF89B2E78059C000A2D41 /* UIImage+OpenCV.h */,
				FBACF89C2E78059C000A2D41 /* UIImage+OpenCV.mm */,
			);
			path = SeetaFace;
			sourceTree = "<group>";
		};
		FBACF8C52E7808DD000A2D41 /* FrameWork */ = {
			isa = PBXGroup;
			children = (
				FBACF8BE2E7808DD000A2D41 /* libjpeg.a */,
				FBACF8C02E7808DD000A2D41 /* SeetaAuthorize.framework */,
				FBACF8C12E7808DD000A2D41 /* SeetaFaceDetector600.framework */,
				FBACF8C22E7808DD000A2D41 /* SeetaFaceLandmarker600.framework */,
				FBACF8C32E7808DD000A2D41 /* SeetaFaceRecognizer610.framework */,
				FBACF8C42E7808DD000A2D41 /* tennis.framework */,
			);
			name = FrameWork;
			path = Runner/SeetaFace/FrameWork;
			sourceTree = "<group>";
		};
		FE6CFE2A2DA4C79E001F8DAC /* Base.lproj */ = {
			isa = PBXGroup;
			children = (
				FE6CFE272DA4C79E001F8DAC /* LaunchScreen.storyboard */,
				FE6CFE292DA4C79E001F8DAC /* Main.storyboard */,
			);
			path = Base.lproj;
			sourceTree = "<group>";
		};
		FEF8E6D32D54987B0006FF1A /* UMSwift */ = {
			isa = PBXGroup;
			children = (
				FEF8E6CF2D54987B0006FF1A /* UMAnalyticsSwift.swift */,
				FEF8E6D12D54987B0006FF1A /* UMCommonSwift.swift */,
			);
			path = UMSwift;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		331C8080294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */;
			buildPhases = (
				C923878533C26A21452F932B /* [CP] Check Pods Manifest.lock */,
				331C807D294A63A400263BE5 /* Sources */,
				331C807F294A63A400263BE5 /* Resources */,
				8C1F908EB9CE85E2D9F9F5AC /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				331C8086294A63A400263BE5 /* PBXTargetDependency */,
			);
			name = RunnerTests;
			productName = RunnerTests;
			productReference = 331C8081294A63A400263BE5 /* RunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				303B1DD5810974FF3EFBDF93 /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				FBACF8F02E780E00000A2D41 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				9DCF1904BDD6C3AA9881A6C8 /* [CP] Embed Pods Frameworks */,
				67FA9B22A684FC2A364AFAFE /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				FBACF8852E78054A000A2D41 /* assert */,
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					331C8080294A63A400263BE5 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 97C146ED1CF9000F007C117D;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 15.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				331C8080294A63A400263BE5 /* RunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		331C807F294A63A400263BE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				FBACF94B2E78110F000A2D41 /* assert in Resources */,
				FE6CFE2B2DA4C79E001F8DAC /* Main.storyboard in Resources */,
				FE6CFE2C2DA4C79E001F8DAC /* LaunchScreen.storyboard in Resources */,
				5BFEDC3D2CC642C1005FEF9C /* ATAuthSDK.bundle in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		303B1DD5810974FF3EFBDF93 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin\n";
		};
		67FA9B22A684FC2A364AFAFE /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		9DCF1904BDD6C3AA9881A6C8 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C923878533C26A21452F932B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RunnerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		331C807D294A63A400263BE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				5BFCA9F92CB8C7B00003D8B0 /* AliAuthHandler.m in Sources */,
				FEB2294A2CFCB6E200F9DAA8 /* HealthKitPlugin.swift in Sources */,
				FEA12BF42E77E66C003AA7D2 /* SeetaFacePlugin.m in Sources */,
				FEF8E6D42D54987B0006FF1A /* UMCommonSwift.swift in Sources */,
				FBACF8A02E78059C000A2D41 /* UIImage+OpenCV.mm in Sources */,
				FBACF8A12E78059C000A2D41 /* beginViedoCompareViewController.mm in Sources */,
				FBACF8A22E78059C000A2D41 /* FFVideoCapturer.m in Sources */,
				FBACF8A32E78059C000A2D41 /* FaceRecognizerManagers.mm in Sources */,
				FBACF8A52E78059C000A2D41 /* CSRImageViewController.mm in Sources */,
				FEF8E6D52D54987B0006FF1A /* UMAnalyticsSwift.swift in Sources */,
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		331C8086294A63A400263BE5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97C146ED1CF9000F007C117D /* Runner */;
			targetProxy = 331C8085294A63A400263BE5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		FE6CFE272DA4C79E001F8DAC /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				FE6CFE262DA4C79E001F8DAC /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		FE6CFE292DA4C79E001F8DAC /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				FE6CFE282DA4C79E001F8DAC /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2025082901;
				DEVELOPMENT_TEAM = 42X4872J7N;
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 1.2.8;
				FLUTTER_BUILD_NUMBER = 2025082901;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Runner/FrameWork",
					"$(PROJECT_DIR)/Runner/SeetaFace/FrameWork",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "新途径人";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/FrameWork",
					"$(PROJECT_DIR)/Runner/SeetaFace/FrameWork",
				);
				MARKETING_VERSION = 1.2.8;
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu99 gnu++11";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WechatOpenSDK\"",
					"-l\"c++\"",
					"-l\"sqlite3.0\"",
					"-l\"z\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"fluwx\"",
					"-framework",
					"\"package_info\"",
					"-framework",
					"\"package_info_plus\"",
					"-framework",
					"\"shared_preferences_foundation\"",
					"-framework",
					"\"url_launcher_ios\"",
					"-framework",
					"\"video_player_avfoundation\"",
					"-framework",
					"\"webview_flutter_wkwebview\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.NextPathway.cn;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		331C8088294A63A400263BE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 80A5885CA7173881FDA62581 /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu99 gnu++11";
				PRODUCT_BUNDLE_IDENTIFIER = com.example.npemployee.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Debug;
		};
		331C8089294A63A400263BE5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 877E6798EEB4B69530AB10FE /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu99 gnu++11";
				PRODUCT_BUNDLE_IDENTIFIER = com.example.npemployee.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Release;
		};
		331C808A294A63A400263BE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F1108AB59AD4006870CA27D5 /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu99 gnu++11";
				PRODUCT_BUNDLE_IDENTIFIER = com.example.npemployee.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerDebug.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "Apple Development";
				CURRENT_PROJECT_VERSION = 2025082901;
				DEVELOPMENT_TEAM = 42X4872J7N;
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 1.2.8;
				FLUTTER_BUILD_NUMBER = 2025082901;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Runner/FrameWork",
					"$(PROJECT_DIR)/Runner/SeetaFace/FrameWork",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "新途径人";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				"IPHONEOS_DEPLOYMENT_TARGET[sdk=iphoneos*]" = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/FrameWork",
					"$(PROJECT_DIR)/Runner/SeetaFace/FrameWork",
				);
				MARKETING_VERSION = 1.2.8;
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu99 gnu++11";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WechatOpenSDK\"",
					"-l\"c++\"",
					"-l\"sqlite3.0\"",
					"-l\"z\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"fluwx\"",
					"-framework",
					"\"package_info\"",
					"-framework",
					"\"package_info_plus\"",
					"-framework",
					"\"shared_preferences_foundation\"",
					"-framework",
					"\"url_launcher_ios\"",
					"-framework",
					"\"video_player_avfoundation\"",
					"-framework",
					"\"webview_flutter_wkwebview\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.NextPathway.cn;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/RunnerRelease.entitlements;
				CURRENT_PROJECT_VERSION = 2025082901;
				DEVELOPMENT_TEAM = 42X4872J7N;
				ENABLE_BITCODE = NO;
				ENABLE_MODULE_VERIFIER = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				FLUTTER_BUILD_NAME = 1.2.8;
				FLUTTER_BUILD_NUMBER = 2025082901;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Runner/FrameWork",
					"$(PROJECT_DIR)/Runner/SeetaFace/FrameWork",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "新途径人";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Runner/FrameWork",
					"$(PROJECT_DIR)/Runner/SeetaFace/FrameWork",
				);
				MARKETING_VERSION = 1.2.8;
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu99 gnu++11";
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"WechatOpenSDK\"",
					"-l\"c++\"",
					"-l\"sqlite3.0\"",
					"-l\"z\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"Security\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"WebKit\"",
					"-framework",
					"\"fluwx\"",
					"-framework",
					"\"package_info\"",
					"-framework",
					"\"package_info_plus\"",
					"-framework",
					"\"shared_preferences_foundation\"",
					"-framework",
					"\"url_launcher_ios\"",
					"-framework",
					"\"video_player_avfoundation\"",
					"-framework",
					"\"webview_flutter_wkwebview\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.NextPathway.cn;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				331C8088294A63A400263BE5 /* Debug */,
				331C8089294A63A400263BE5 /* Release */,
				331C808A294A63A400263BE5 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
