import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/device_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/campus_training/campus_training_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';

class CampusTrainingPage extends StatefulWidget {
  final String title;
  const CampusTrainingPage({super.key, required this.title});

  @override
  State<CampusTrainingPage> createState() => _CampusTrainingPageState();
}

class _CampusTrainingPageState extends State<CampusTrainingPage> {
  int _tabIndex = 0;
  final List<GlobalKey> _tabKeyList = [];
  List<EdgesModel> tagTabs = [];
  List<Map<String, List<CampusTrainingCourseModel>>> courseList = []; //推荐列表使用
  List<CampusTrainingCourseModel> courseListAll = []; //具体分类列表使用

  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  final PageController _pageController = PageController();
  final ScrollController _scrollController = ScrollController();

  final EasyRefreshController _refreshController = EasyRefreshController();

  void _autoScrollTabToVisible() {
    final RenderBox? tabRenderBox =
        _tabKeyList[_tabIndex].currentContext?.findRenderObject() as RenderBox?;
    if (tabRenderBox != null) {
      final tabPosition = tabRenderBox.localToGlobal(Offset.zero).dx;
      final tabWidth = tabRenderBox.size.width;
      final screenWidth = MediaQuery.of(context).size.width;
      final scrollOffset = _scrollController.offset;

      if (tabPosition < 0) {
        _scrollController.animateTo(scrollOffset + tabPosition - 16.w,
            duration: const Duration(milliseconds: 300), curve: Curves.ease);
      } else if (tabPosition + tabWidth > screenWidth) {
        _scrollController.animateTo(
            scrollOffset + (tabPosition + tabWidth - screenWidth) + 16.w,
            duration: const Duration(milliseconds: 300),
            curve: Curves.ease);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getCourseTagList();
  }

  void _getCourseTagList() {
    UserServiceProvider().getSchoolTagList(
      cacheCallBack: (value) {
        _formatTagsData(value, true);
      },
      successCallBack: (value) {
        _formatTagsData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  void _getCourseList({int? tag_id, String? keyword}) {
    //tag_id 不传查推荐
    UserServiceProvider().getSchoolCourseList(
      tag_id: tag_id,
      cacheCallBack: (value) {
        _formatCourseListData(value, true);
      },
      successCallBack: (value) {
        _formatCourseListData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  _formatTagsData(ResultData? value, bool isCache) {
    tagTabs.clear();
    for (var e in value?.data) {
      e['tag']['edges']['children'].insert(0, {
        'id': null,
        'created_at': '',
        'updated_at': '',
        'name': '推荐',
        'sort': 0,
        'parent_id': -1,
        'status': -1,
        'level': 2
      });
      CampusTrainingTagModel tagModel =
          CampusTrainingTagModel.formJson(e['tag']);
      tagTabs = tagModel.edges;
      tagTabs.sort((a, b) => a.sort.compareTo(b.sort));
    }
    if (tagTabs.isNotEmpty) {
      _tabKeyList.clear();
      _tabKeyList.addAll(tagTabs.map((e) => GlobalKey()));
    }
    _getCourseList();
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  _formatCourseListData(ResultData? value, bool isCache) {
    if (_tabIndex == 0) {
      courseList.clear();
      for (var tag in tagTabs) {
        if (tag.id != null) {
          courseList.add({tag.name: []});
        }
      }
      for (var e in value?.data) {
        CampusTrainingCourseModel courseListModel =
            CampusTrainingCourseModel.formJson(e);
        for (var tag in tagTabs) {
          if (courseListModel.tag_ids.contains('${tag.id}')) {
            for (var element in courseList) {
              if (element.containsKey(tag.name)) {
                element[tag.name]!.add(courseListModel);
              }
            }
          }
        }
      }
      courseList.retainWhere((e) => e.values.first.isNotEmpty);
    } else {
      courseListAll.clear();
      for (var e in value?.data) {
        CampusTrainingCourseModel courseListModel =
            CampusTrainingCourseModel.formJson(e);
        courseListAll.add(courseListModel);
      }
    }

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FD),
      appBar: CommonNav(title: widget.title, bacColor: const Color(0xFFF7F8FD)),
      body: Column(
        children: [
          SizedBox(height: 16.h),
          _tabView(),
          SizedBox(height: 12.h),
          Expanded(
              child: PageView.builder(
                  controller: _pageController,
                  itemCount: tagTabs.length,
                  onPageChanged: (value) {
                    setState(() {
                      _tabIndex = value;
                    });
                    _autoScrollTabToVisible();
                    _getCourseList(tag_id: tagTabs[_tabIndex].id);
                  },
                  itemBuilder: (pC, pI) {
                    return EasyRefresh.builder(
                      controller: _refreshController,
                      onRefresh: () =>
                          _getCourseList(tag_id: tagTabs[_tabIndex].id),
                      childBuilder: (_, physic) => _tabIndex == 0
                          ? courseList.isEmpty
                              ? NoDataPage(
                                  physics: physic,
                                )
                              : ListView.builder(
                                  physics: physic,
                                  itemCount: courseList.length,
                                  itemBuilder: _itemBuilder)
                          : courseListAll.isEmpty
                              ? NoDataPage(
                                  physics: physic,
                                )
                              : ListView.builder(
                                  physics: physic,
                                  itemCount: courseListAll.length,
                                  itemBuilder: _itemBuilder),
                    );
                  }))
        ],
      ),
    );
  }

  Widget _tabView() {
    return Container(
        width: 1.sw,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: SingleChildScrollView(
          controller: _scrollController,
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              ...tagTabs.map((e) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: VideoTabItem(
                      key: _tabKeyList[tagTabs.indexOf(e)],
                      title: e.name,
                      isSelect: _tabIndex == tagTabs.indexOf(e),
                      onTap: () {
                        setState(() {
                          _tabIndex = tagTabs.indexOf(e);
                        });
                        _pageController.animateToPage(_tabIndex,
                            duration: const Duration(milliseconds: 200),
                            curve: Curves.ease);
                        _getCourseList(tag_id: e.id);
                      }),
                );
              }),
            ],
          ),
        ));
  }

  Widget _itemBuilder(c, index) {
    if (_tabIndex == 0) {
      Map<String, List<CampusTrainingCourseModel>> model = courseList[index];
      return Container(
        margin: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
        padding: EdgeInsets.only(left: 16.w, top: 6.h, bottom: 16.h),
        width: ScreenUtil().screenWidth,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(16.r)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(model.keys.first,
                    style:
                        TextStyle(color: '#333333'.toColor(), fontSize: 17.sp)
                            .phBold),
                SizedBox(
                  height: 40.h,
                  child: TextButton(
                      onPressed: () {
                        setState(() {
                          _tabIndex = tagTabs
                              .indexWhere((e) => e.name == model.keys.first);
                        });
                        _pageController.animateToPage(_tabIndex,
                            duration: const Duration(milliseconds: 200),
                            curve: Curves.ease);
                        _getCourseList(tag_id: tagTabs[_tabIndex].id);
                        _autoScrollTabToVisible();
                      },
                      child: Row(
                        children: [
                          Text('更多',
                              style: TextStyle(
                                      color: '#909090'.toColor(),
                                      fontSize: 14.sp)
                                  .phRegular),
                          const Icon(
                            Icons.arrow_forward_ios,
                            size: 12,
                            color: Color(0xFF909090),
                          ),
                        ],
                      )),
                ),
              ],
            ),
            SizedBox(height: 19.h),
            ...model.values.first.map(
              (e) => GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  CourseListModel courseListModel = CourseListModel(
                      id: e.id,
                      course_type: e.course_type,
                      image: e.image,
                      introduction: e.introduction,
                      name: e.name,
                      phrase: e.phrase,
                      status: e.status,
                      tag_ids: e.tag_ids);
                  NavigatorUtils.push(context, StudyRouter.videoPlay,
                      arguments: {'model': courseListModel});
                },
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _getImg(e.image),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            e.name,
                            style: TextStyle(
                                    color: const Color(0xFF323640),
                                    fontSize: 14.sp)
                                .pfSemiBold,
                          ),
                          SizedBox(height: 4.h),
                          Text(DateTimeUtils.formatDataToYmd(e.start_time),
                              style: TextStyle(
                                      color: '#9B9FAD'.toColor(),
                                      fontSize: 12.sp)
                                  .pfRegular),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      CampusTrainingCourseModel model = courseListAll[index];
      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          CourseListModel courseListModel = CourseListModel(
              id: model.id,
              course_type: model.course_type,
              image: model.image,
              introduction: model.introduction,
              name: model.name,
              phrase: model.phrase,
              status: model.status,
              tag_ids: model.tag_ids);
          NavigatorUtils.push(context, StudyRouter.videoPlay,
              arguments: {'model': courseListModel});
        },
        child: Container(
          margin: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          width: ScreenUtil().screenWidth,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(16.r)),
          child: Column(
            children: [
              Row(
                children: [
                  _getImg(model.image),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  model.name,
                                  style: TextStyle(
                                    color: const Color(0xFF323640),
                                    fontSize: 14.sp,
                                  ).pfMedium,
                                ),
                                SizedBox(height: 4.h),
                                Text(
                                    DateTimeUtils.formatDataToYmd(
                                        model.start_time),
                                    style: TextStyle(
                                        color: '#9B9FAD'.toColor(),
                                        fontSize: 12.sp)),
                              ],
                            )),
                          ],
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _getImg(String imageUrl) {
    return isLandscapeCache.containsKey(imageUrl)
        ? ClipRRect(
            borderRadius: BorderRadius.circular(10.r),
            child: CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.cover,
              width: isLandscapeCache[imageUrl]!
                  ? (DeviceUtils.isTablet(context) ? 140.w : 119.w)
                  : 73.w,
              height: isLandscapeCache[imageUrl]! ? 64.h : 102.h,
            ),
          )
        : FutureBuilder(
            future: _isLandscape(imageUrl),
            builder: (_, snapData) {
              if (snapData.connectionState == ConnectionState.done) {
                final isLandscape = snapData.data ?? true;
                return ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.cover,
                    height: isLandscape ? 64.h : 102.h,
                    width: isLandscape
                        ? (DeviceUtils.isTablet(context) ? 140.w : 119.w)
                        : 73.w,
                  ),
                );
              }
              return Container();
            });
  }

  // 判断图片是否为横向
  Future<bool> _isLandscape(String imageUrl) async {
    final Completer<bool> completer = Completer();
    final Image image = Image.network(imageUrl);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        final bool isLandscape = info.image.width > info.image.height;
        isLandscapeCache[imageUrl] = isLandscape;
        completer.complete(isLandscape);
      }),
    );

    return completer.future;
  }
}
