import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CheckInChannel {
  static CheckInChannel? _instance;
  static late MethodChannel _channel;

  CheckInChannel._internal() {
    _channel = const MethodChannel('check_in_plugin');
    debugPrint('CheckInChannel init');
  }
  factory CheckInChannel() {
    _instance ??= CheckInChannel._internal();
    return _instance!;
  }

  Future<T> sentToNative<T>(String methodName, Map<String, String> arguments) async {
    final result = await _channel.invokeMethod(methodName, arguments);
    return result;
  }

  Future<void> addListen({Function()? exampleCallback}) async {
    _channel.setMethodCallHandler((call) {
      if (call.method == "exampleCallBack") {
        debugPrint("mmmmmmmmmmmmmmmm, 收到native端发送的消息1");
        exampleCallback?.call();
      }
      return Future.value();
    });
  }
}
