package com.xtj.person.common.viewmodel

import androidx.lifecycle.MutableLiveData
import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.bean.SchoolListBean
import com.xtj.person.common.ext.rxHttpRequest
import com.xtj.person.common.net.repository.DataRepository

class SelectLocationDialogViewModel: BaseViewModel() {
    val schoolListBeanLiveData = MutableLiveData<SchoolListBean>()

    fun getSchoolList(keyword:String,size:Int,page:Int){
        rxHttpRequest {
            onRequest={
              schoolListBeanLiveData.value = DataRepository.getSchoolList(page,size,keyword).await()

            }

            onError={

            }
        }
    }

}