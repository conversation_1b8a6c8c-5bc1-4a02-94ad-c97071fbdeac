package com.xtj.person.activity

import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.viewbinding.ViewBinding
import com.gyf.immersionbar.ImmersionBar
import com.gyf.immersionbar.ktx.immersionBar
import com.kingja.loadsir.core.LoadService
import com.kingja.loadsir.core.LoadSir
import com.xtj.person.R
import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.ext.addActivity
import com.xtj.person.common.ext.dismissLoadingExt
import com.xtj.person.common.ext.getColorExt
import com.xtj.person.common.ext.showLoadingExt
import com.xtj.person.common.ext.toast
import com.xtj.person.common.ext.viewBinding
import com.xtj.person.common.net.Error403Code
import com.xtj.person.common.net.network.NetState
import com.xtj.person.common.net.network.NetworkStateManager
import com.xtj.person.common.util.state.EmptyCallback
import com.xtj.person.common.util.state.ErrorCallback
import com.xtj.person.common.util.state.LoadingCallback
import com.xtj.person.common.viewmodel.AppViewModel
import com.xtj.person.common.viewmodel.getAppViewModel
import com.xtj.person.common.viewmodel.getVmClazz
import com.xtj.person.common.widget.CustomToolBar
import com.xtj.person.common.base.BaseIView
import com.xtj.person.common.ext.visibleOrGone
import com.xtj.person.common.net.entity.base.LoadStatusBean
import com.xtj.person.common.net.entity.base.LoadingDialogBean
import com.xtj.person.common.net.entity.loadingtype.LoadingType
import com.xtj.person.databinding.ActivityBaseBinding

abstract class BaseVmActivity<VM : BaseViewModel, VB : ViewBinding> : BaseActivity<Any?, Any?>(),
    Error403Code.Call403Back,
    BaseIView {

    /**
     * 子activity的viewBinding
     */
    lateinit var subBinding: VB

    override val binding by viewBinding(ActivityBaseBinding::inflate)

    //界面状态管理者
    protected lateinit var uiStatusManger: LoadService<*>

    //当前Activity绑定的 ViewModel
    lateinit var mViewModel: VM

    //toolbar 这个可替换成自己想要的标题栏
    lateinit var mToolbar: CustomToolBar

    // Application全局的ViewModel，里面存放了一些账户信息，基本配置信息等
    val appViewModel: AppViewModel by lazy { getAppViewModel<AppViewModel>() }

    override fun onCreate(savedInstanceState: Bundle?) {

        detectIsTablet()
        super.onCreate(savedInstanceState)
        subBinding = getViewBinding(layoutInflater)
        setContentView(binding.root)
        initStatusBar()
        //生成ViewModel
        mViewModel = createViewModel()
        //初始化 status View
        initStatusView(savedInstanceState)
        // 初始化监听事件
        initListener()
        //注册界面响应事件
        initLoadingUiChange()
        //初始化绑定observer
        initObserver()
        NetworkStateManager.instance.mNetworkStateCallback.observeInActivity(this, Observer {
            onNetworkStateChanged(it)
        })
        //初始化请求成功方法
        onRequestSuccess()
        //初始化绑定点击方法
        onBindViewClick()
        //添加自身到队列
        addActivity(this)






        Error403Code.set403CallBack(this)

    }


    /**
     * 全局403跳转到登录页面
     */
    override fun callBack403() {
//        when {
//            ActivityUtils.getTopActivity() !is YxbLoginActivity -> {
//                setLoginToken("")
//                finishAllActivities()
//                toStartActivity(YxbLoginActivity::class.java)
//            }
//        }
    }
    private fun detectIsTablet(){
//        if(!SystemUtil.isLenovoPad()){
//            requestedOrientation = if (isTablet()) {
//                ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
//            } else {
//                ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
//            }
//        }
    }

    override fun onResume() {
        super.onResume()

    }






    private fun getSystemMetrics(): DisplayMetrics {
        return applicationContext.resources.displayMetrics;
    }

    /**
     * 网络变化监听 子类重写
     */
    open fun onNetworkStateChanged(netState: NetState) {}

    /**
     * 必须实现的方法，用于初始化页面对应的ViewBinding对象
     */
    protected abstract fun getViewBinding(inflater: LayoutInflater): VB

    /**
     * 设置状态栏
     */
    open fun initStatusBar() {
        // 所有子类都将继承这些相同的属性,请在设置界面之后设置
        ImmersionBar.with(this).init()
        immersionBar {
            statusBarColor(getStatusBarColor())
            navigationBarColor(R.color.white)
            statusBarDarkFont(getStatusBarDarkFont())
            barColor(getBarColor())
            fitsSystemWindows(true)
        }
    }

    /**
     * 状态栏颜色 默认白色 需改变请覆写该方法
     */
    open fun getStatusBarColor() = R.color.white

    /**
     * 状态栏颜色与导航栏颜色 默认为白色 需改变请复写改方法
     */
    open fun getBarColor() = R.color.white

    /**
     * 状态栏字体颜色 默认深色 需改变请复写此方法
     */
    open fun getStatusBarDarkFont() = true

    private fun initStatusView(savedInstanceState: Bundle?) {
        mToolbar = binding.baseToolBar
        mToolbar.run {
            setBackgroundColor(getColorExt(R.color.grayF8))
            //是否隐藏标题栏
            visibleOrGone(showToolBar())
        }
        initImmersionBar()
        binding.baseContentView.addView(subBinding.root)
        uiStatusManger = LoadSir.getDefault()
            .register(if (getLoadingView() == null) binding.baseContentView else getLoadingView()!!) {
                onLoadRetry()
            }
        binding.baseContentView.post {
            initView(savedInstanceState)
        }
    }

    /**
     * 初始化view
     */
    abstract fun initView(savedInstanceState: Bundle?)

    /**
     * 创建监听事件
     */
    open fun initListener() {}



    /**
     * 创建观察者
     */
    open fun initObserver() {}

    /**
     * 创建viewModel
     */
    private fun createViewModel(): VM {
        return ViewModelProvider(this).get(getVmClazz(this))
    }

    /**
     * 是否显示 标题栏 默认隐藏
     */
    open fun showToolBar(): Boolean {
        return false
    }


    /**
     * 初始化沉浸式
     * Init immersion bar.
     */
    protected open fun initImmersionBar() {
        //设置共同沉浸式样式
        if (showToolBar()) {
            mToolbar.setBackgroundColor(getColorExt(R.color.colorPrimary))
            setSupportActionBar(mToolbar.getBaseToolBar())
            ImmersionBar.with(this).titleBar(mToolbar).init()
        }
    }

    /**
     * 点击事件方法 配合setOnclick()拓展函数调用，做到黄油刀类似的点击事件
     */
    open fun onBindViewClick() {}

    /**
     * 注册 UI 事件
     */
    private fun initLoadingUiChange() {
        mViewModel.loadingChange.run {
            loading.observeInActivity(this@BaseVmActivity) {
                if (it.loadingType == LoadingType.LOADING_DIALOG) {
                    if (it.isShow) {
                        showLoadingExt()
                    } else {
                        dismissLoadingExt()
                    }
                    return@observeInActivity
                }
                if (it.loadingType == LoadingType.LOADING_CUSTOM) {
                    if (it.isShow) {
                        showCustomLoading(it)
                    } else {
                        dismissCustomLoading(it)
                    }
                    return@observeInActivity
                }
                if (it.loadingType == LoadingType.LOADING_XML) {
                    if (it.isShow) {
                        showLoadingUi()
                    }
                    return@observeInActivity
                }
            }
            showEmpty.observeInActivity(this@BaseVmActivity) {
                onRequestEmpty(it)
            }
            showError.observeInActivity(this@BaseVmActivity) {
                // 如果请求错误 并且loading类型为 xml 那么控制界面显示为错误布局
                if (it.loadingType == LoadingType.LOADING_XML) {
                    showErrorUi(it.errorMessage)
                }
                onRequestError(it)
            }
            showSuccess.observeInActivity(this@BaseVmActivity) {
                showSuccessUi()
            }
        }
    }

    /**
     * 请求列表数据为空时 回调
     * @param loadStatus LoadStatusEntity
     */
    override fun onRequestEmpty(loadStatus: LoadStatusBean) {
        showEmptyUi()
    }

    /**
     * 请求接口失败回调，如果界面有请求接口，需要处理错误业务，请实现它，如果不实现，默认toast错误消息
     * @param loadStatus LoadStatusEntity
     */
    override fun onRequestError(loadStatus: LoadStatusBean) {
        loadStatus.errorMessage.toast()
    }

    /**
     * 请求成功的回调放在这里初始化
     */
    override fun onRequestSuccess() {}

    /**
     * 空界面，错误界面 点击重试时触发的方法，如果有使用状态布局的话，一般子类都要实现
     */
    override fun onLoadRetry() {}

    /**
     * 显示 成功状态界面
     */
    override fun showSuccessUi() {
        uiStatusManger.showSuccess()
    }

    /**
     * 显示 错误 状态界面
     */
    override fun showErrorUi(errMessage: String) {
        uiStatusManger.showCallback(ErrorCallback::class.java)
    }


    /**
     * 显示 错误 状态界面
     */
    override fun showEmptyUi() {
        uiStatusManger.showCallback(EmptyCallback::class.java)
    }

    /**
     * 显示 loading 状态界面
     */
    override fun showLoadingUi() {
        uiStatusManger.showCallback(LoadingCallback::class.java)
    }

    /**
     * 子类可传入需要被包裹的View，做状态显示-空、错误、加载
     * 如果子类不覆盖该方法 那么会将整个当前Activity界面（除封装的头部Toolbar）都当做View包裹
     */
    open fun getLoadingView(): View? {
        return null
    }

    /**
     * 显示自定义loading
     */
    override fun showCustomLoading(setting: LoadingDialogBean) {
        showLoadingExt(setting.loadingMessage,setting.isCustomLoadingOutSideCancelable)

    }

    /**
     * 隐藏自定义loading
     */
    override fun dismissCustomLoading(setting: LoadingDialogBean) {
        dismissLoadingExt()
    }



    override fun onDestroy() {
        super.onDestroy()
    }

}