package com.xtj.person.common.ext

import com.scwang.smart.refresh.layout.SmartRefreshLayout


fun SmartRefreshLayout.refresh(refreshAction:()-> Unit = {}):SmartRefreshLayout{
    this.setOnRefreshListener {
        refreshAction.invoke()
    }
    return this
}
fun SmartRefreshLayout.loadMore(loadMoreAction:()-> Unit = {}):SmartRefreshLayout{
    this.setOnLoadMoreListener {
        loadMoreAction.invoke()
    }
    return this
}

