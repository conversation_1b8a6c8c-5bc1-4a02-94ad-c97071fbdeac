package com.xtj.person.activity

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.common.ext.dismissLoadingExt
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.showLoadingExt
import com.xtj.person.common.ext.visible
import com.xtj.person.common.util.ImageViewUtils
import com.xtj.person.common.viewmodel.StudentInfoEditViewModel
import com.xtj.person.databinding.ActivityStudentInfoEditBinding

class StudentInfoEditActivity: BaseVmActivity<StudentInfoEditViewModel, ActivityStudentInfoEditBinding>(),
    View.OnClickListener{
    var userName = ""
    var idNumber = ""
    var guid = ""
    var icon = ""
    var phoneNum = ""

    var localIconFile:String = ""


    private val takePictureLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                 localIconFile = result.data?.getStringExtra("imagePath")?:""
                ImageViewUtils.loadImg(localIconFile,subBinding.ivUserImg)
                subBinding.ivUploadImgHint.gone()
                subBinding.tvUploadImgHint.gone()
                subBinding.tvChangeImg.visible()
                Log.d("StudentInfoEdit", "拍照返回路径: $localIconFile")
                // 这里可以把 imagePath 设置到 ImageView 或上传服务器
            }
        }


    override fun getViewBinding(inflater: LayoutInflater): ActivityStudentInfoEditBinding {

        return ActivityStudentInfoEditBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {
        userName =  intent.getStringExtra("userName" )?:""
        idNumber =  intent.getStringExtra("idNumber" )?:""
        guid =  intent.getStringExtra("guid" )?:""
        icon =  intent.getStringExtra("icon" )?:""
        phoneNum =  intent.getStringExtra("phoneNum" )?:""


        if(!icon.isNullOrEmpty()){
            ImageViewUtils.loadImg(icon,subBinding.ivUserImg)
            subBinding.tvChangeImg.visible()
        }else{
            subBinding.tvChangeImg.gone()
        }

        subBinding.run {
            include.title.text = "录入学员信息"
            include.backBtn.setOnClickListener { finish() }
            tvChangeImg.setOnClickListener(this@StudentInfoEditActivity)

            tvUploadImgHint.setOnClickListener(this@StudentInfoEditActivity)
            ivUploadImgHint.setOnClickListener(this@StudentInfoEditActivity)

            etName.setText(userName)
            etIdCardNum.setText(idNumber)
            tvId.text = guid
            ImageViewUtils.loadImg(icon,ivUserImg)

            tvCommit.setOnClickListener(this@StudentInfoEditActivity)
        }
    }

    override fun onClick(v: View?) {
        subBinding.run {
            when(v){
                tvUploadImgHint,ivUploadImgHint,tvChangeImg->{
                    var intent = Intent(this@StudentInfoEditActivity, TakePictureActivity::class.java)
                    takePictureLauncher.launch(intent)


                }

                tvCommit->{
                    if(etName.text.isNullOrEmpty()){
                        ToastUtils.showShort("请填写姓名")
                    }else if(etIdCardNum.text.isNullOrEmpty()){
                        ToastUtils.showShort("请填写身份证号")
                    }else if(icon.isNullOrEmpty()&&localIconFile.isNullOrEmpty()){
                        ToastUtils.showShort("请上传照片")
                    }else{
                        showLoadingExt("上传中....")
                        mViewModel.changeUserInfo(guid,etName.text.toString(),etIdCardNum.text.toString(),phoneNum,localIconFile)
                    }

                }
            }
        }

    }

    override fun initObserver() {
        super.initObserver()
        mViewModel.changeUserInfoFailLiveData.observe (this){
            dismissLoadingExt()
            ToastUtils.showShort("上传失败")

        }

        mViewModel.changeUserInfoResultLiveData.observe (this){
            dismissLoadingExt()
            if(it.code==200){
                ToastUtils.showShort("上传成功")
                finish()
            }else{
                if(it.message.isNullOrEmpty()){
                    ToastUtils.showShort("上传失败")
                }else{
                    ToastUtils.showShort(it.message)
                }
            }
        }
    }

}