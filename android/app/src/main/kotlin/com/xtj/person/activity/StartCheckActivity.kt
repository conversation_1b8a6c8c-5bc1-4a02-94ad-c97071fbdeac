package com.xtj.person.activity

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Paint
import android.os.Bundle
import android.util.Log
import android.util.Rational
import android.view.LayoutInflater
import android.view.View
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.core.UseCaseGroup
import androidx.camera.core.ViewPort
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.R
import com.xtj.person.adapter.RvNewestCheckInResultAdapter
import com.xtj.person.common.bean.NewestCheckInResultItem
import com.xtj.person.common.ext.getColorExt
import com.xtj.person.common.net.repository.DataRepository.getNewestCheckInResult
import com.xtj.person.common.util.CameraUtils.chooseAspectRatio
import com.xtj.person.common.util.CameraUtils.cosine
import com.xtj.person.common.util.CameraUtils.cropBitmap
import com.xtj.person.common.util.CameraUtils.expandCropArea
import com.xtj.person.common.util.CameraUtils.imageProxyToBitmap
import com.xtj.person.common.util.CameraUtils.mirrorBitmap
import com.xtj.person.common.util.CameraUtils.rotateBitmap
import com.xtj.person.common.util.CameraUtils.transformCoordinates
import com.xtj.person.common.util.CameraUtils.yuv420888ToNV21
import com.xtj.person.common.util.JniUtils.detectNV21
import com.xtj.person.common.util.JniUtils.extractNV21
import com.xtj.person.common.util.JniUtils.seetaSimilarity
import com.xtj.person.common.viewmodel.StartCheckViewModel
import com.xtj.person.databinding.ActivityStartCheckLayoutBinding
import java.util.concurrent.Executors

class StartCheckActivity: BaseVmActivity<StartCheckViewModel, ActivityStartCheckLayoutBinding>(), View.OnClickListener {

    data class Entry(val name: String, val feat: FloatArray)
    private val db = mutableListOf<Entry>()
    private var lastNV21: ByteArray? = null
    private var frameW: Int = 0
    private var frameH: Int = 0
    private var frameRotation: Int = 0
    private val ENABLE_RECOG = true // 启用识别功能
    private var isFrontCamera: Boolean = true
    @Volatile private var cachedScaleType: PreviewView.ScaleType = PreviewView.ScaleType.FILL_START
    @Volatile private var viewW: Int = 0
    @Volatile private var viewH: Int = 0
    private var cameraProvider: ProcessCameraProvider? = null
    private var cameraExecutor = Executors.newSingleThreadExecutor()
    private var camera: Camera? = null

    private var curriculumId:Int = -1
    private var curriculumName:String=""
    private var period:String = ""

    private var newestCheckInResultBeans:ArrayList<NewestCheckInResultItem> = arrayListOf()
    private var rvNewestCheckInResultAdapter: RvNewestCheckInResultAdapter?=null


    override fun getViewBinding(inflater: LayoutInflater): ActivityStartCheckLayoutBinding {

        return ActivityStartCheckLayoutBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {
        curriculumName = intent.getStringExtra("curriculumName")?:""
        period = intent.getStringExtra("period")?:""
        curriculumId = intent.getIntExtra("curriculumId",-1)

        subBinding.run {
            include.title.text = curriculumName
            include.backBtn.setOnClickListener { finish() }
            include.rightBtn.text="人脸录入"
            include.rightBtn.setOnClickListener {
               var intent = Intent(this@StartCheckActivity,QueryUserByPhoneActivity::class.java)
                startActivity(intent)
            }
            cachedScaleType = preview.scaleType
            preview.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
                viewW = v.width
                viewH = v.height
            }
            tvCheckInRecord.paintFlags = tvCheckInRecord.paintFlags or Paint.UNDERLINE_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG

            tvCheckInRecord.setOnClickListener(this@StartCheckActivity)

            rvNewestResult.layoutManager = GridLayoutManager(this@StartCheckActivity,4)
            rvNewestCheckInResultAdapter = RvNewestCheckInResultAdapter(newestCheckInResultBeans)
            rvNewestResult.adapter = rvNewestCheckInResultAdapter

        }

        ensurePermissionsAndStart()

        mViewModel.getNewestCheckInResult(curriculumId,period)

    }


    private fun ensurePermissionsAndStart() {
        val perms = arrayOf(Manifest.permission.CAMERA)
        val need = perms.any { ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED }
        if (need) ActivityCompat.requestPermissions(this, perms, 100)
        bindCamera()
    }

    private fun bindCamera() {
        val providerFuture = ProcessCameraProvider.getInstance(this)
        providerFuture.addListener({
            val provider = providerFuture.get()
            // Choose aspect ratio closest to screen for larger FOV
            val dm = resources.displayMetrics
            val screenW = dm.widthPixels
            val screenH = dm.heightPixels
            val ratio = chooseAspectRatio(screenW, screenH)

            val preview = Preview.Builder()
                .setTargetAspectRatio(ratio)
                .setTargetRotation(subBinding.preview.display.rotation)
                .build()
            preview.setSurfaceProvider(subBinding.preview.surfaceProvider)

            // Fill the view to mimic native camera preview experience
            subBinding.preview.scaleType = PreviewView.ScaleType.FILL_CENTER
            // Update cached scale type after setting
            cachedScaleType = subBinding.preview.scaleType

            val analyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setTargetAspectRatio(ratio)
                .setTargetRotation(subBinding.preview.display.rotation)
                .build()
            cameraExecutor = Executors.newSingleThreadExecutor()
            analyzer.setAnalyzer(cameraExecutor) { image ->
                analyzeFrame(image)
                image.close()
            }

            val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA
            isFrontCamera = (cameraSelector == CameraSelector.DEFAULT_FRONT_CAMERA)
            try {
                provider.unbindAll()
                // Use a shared ViewPort so preview and analysis have consistent crop/FOV
                val vpW = if (viewW > 0) viewW else screenW
                val vpH = if (viewH > 0) viewH else screenH
                val rotation = subBinding.preview.display.rotation
                val viewPort = ViewPort.Builder(Rational(vpW, vpH), rotation)
                    .setScaleType(ViewPort.FILL_CENTER)
                    .setLayoutDirection(subBinding.preview.layoutDirection)
                    .build()
                val group = UseCaseGroup.Builder()
                    .addUseCase(preview)
                    .addUseCase(analyzer)
                    .setViewPort(viewPort)
                    .build()
                camera = provider.bindToLifecycle(this as LifecycleOwner, cameraSelector, group)
            } catch (_: Exception) {}
        }, ContextCompat.getMainExecutor(this))
    }





    private fun analyzeFrame(image: ImageProxy) {
        val nv21 = yuv420888ToNV21(image)
        frameW = image.width
        frameH = image.height
        frameRotation = image.imageInfo.rotationDegrees
        lastNV21 = nv21

        // Debug: Log actual dimensions (use cached view size; don't touch View off main thread)
        val previewW = viewW
        val previewH = viewH
        Log.d("DEBUG", "Frame: ${frameW}x${frameH}, Preview: ${previewW}x${previewH}, Rotation: ${frameRotation}")
        val boxes = detectNV21(nv21, frameW, frameH, frameRotation,this@StartCheckActivity)
        val labels = Array(boxes.size/4) { i -> "face_${i}" }
        if (ENABLE_RECOG) {
            // recognize best match for each box
            for (i in 0 until (boxes.size/4)) {
                val x=boxes[i*4]; val y=boxes[i*4+1]; val w=boxes[i*4+2]; val h=boxes[i*4+3]
                val feat = extractNV21(nv21, frameW, frameH, x,y,w,h, frameRotation,this@StartCheckActivity)
                //显示人脸图片
                if (feat.isNotEmpty() && db.isNotEmpty()) {
                    var bestCos = Float.NEGATIVE_INFINITY
                    var bestName = "unknown"
                    var bestSeeta = Float.NEGATIVE_INFINITY
                    var bestEntry:Entry? = null
                    db.forEach { e ->
                        val s = cosine(feat, e.feat)
                        if (s > bestCos) {
                            bestCos = s
                            bestName = e.name
                            // Compare Seeta's built-in similarity for the same pair
                            bestSeeta = try { seetaSimilarity(feat, e.feat,this@StartCheckActivity) } catch (_: Throwable) { Float.NaN }
                            bestEntry = e
                        }
                    }
                    val cosStr = String.format("%.2f", bestCos)
                    val seetaStr = if (bestSeeta.isNaN()) "NaN" else String.format("%.2f", bestSeeta)
//                    labels[i] = if (bestCos > 0.7f) {
//                        "$bestName(cos=$cosStr, seeta=$seetaStr)"
//                    } else {
//                        "cos=$cosStr, seeta=$seetaStr"
//                    }
                    if (bestCos > 0.7f) {
                        ToastUtils.showLong(bestName.toString()+"签到成功")
                        showFaceBitmap(image,boxes,i,feat)
                        labels[i] =   "$bestName 相似度:${cosStr.toFloat()*100}%"
                        db.remove(bestEntry)
                    }
                }
            }
        }

        // Transform coordinates from image analysis to preview display
        val transformedBoxes = transformCoordinates(
            boxes = boxes,
            frameW = frameW,
            frameH = frameH,
            frameRotation = frameRotation,
            viewW = viewW,
            viewH = viewH,
            isFrontCamera = isFrontCamera,
            cachedScaleType = cachedScaleType)
        if (viewW > 0 && viewH > 0) {
            runOnUiThread { subBinding.overlay.setData(transformedBoxes, labels, getColorExt(R.color.redFF0000Alpha80)) }
        }
    }

    fun showFaceBitmap(image: ImageProxy,boxes: IntArray,boxesIndex:Int,feat: FloatArray){
        imageProxyToBitmap(image,this@StartCheckActivity)?.let { bitmap ->
            val rotatedBitmap = rotateBitmap(bitmap, image.imageInfo.rotationDegrees)
            val expandedRect = expandCropArea(
                boxes[boxesIndex*4], boxes[boxesIndex*4+1], boxes[boxesIndex*4+2], boxes[boxesIndex*4+3],
                scale = 0.2f, // 扩大20%
                bitmapWidth = rotatedBitmap.width,
                bitmapHeight = rotatedBitmap.height
            )
            cropBitmap(rotatedBitmap,  expandedRect.left,
                expandedRect.top,
                expandedRect.width(),
                expandedRect.height())?.let { cropBitmap ->
                val mirrorBitMap =  mirrorBitmap(cropBitmap)
                runOnUiThread {
//                    ivFace.setImageBitmap(mirrorBitMap)
                }
            }
            rotatedBitmap.recycle() // 释放原始 Bitmap
        }
    }



    override fun onClick(v: View?) {

        subBinding.run {
            when(v){
                tvCheckInRecord->{
                   var intent = Intent(this@StartCheckActivity, CheckInRecordActivity::class.java)
                    startActivity(intent)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        cameraExecutor.shutdown()
        cameraProvider?.unbindAll()
    }

    override fun initObserver() {
        super.initObserver()
        mViewModel.newestCheckInResultLiveData.observe(this){
            if(it.code==200){
                newestCheckInResultBeans.clear()
                newestCheckInResultBeans.addAll(it.data.list)
                rvNewestCheckInResultAdapter?.notifyDataSetChanged()
                subBinding.tvTotalCheckNum.text = it.data.count.toString()
            }

        }
    }
}