package com.xtj.person.common.ext

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.scwang.smart.refresh.layout.SmartRefreshLayout


/**
 * 作者 : kaitaoyan
 * 时间 : 2021/5/14
 * 描述 :
 */

/**
 * 在activity中初始化ViewPager2
 */
fun ViewPager2.initActivity(
    activity: FragmentActivity,
    fragments: ArrayList<Fragment>,
    isUserInputEnabled: Boolean = true,
): ViewPager2 {
    // 是否可滑动
    this.isUserInputEnabled = isUserInputEnabled
    // 设置适配器
    adapter = object : FragmentStateAdapter(activity) {
        override fun createFragment(position: Int) = fragments[position]
        override fun getItemCount() = fragments.size
    }
    return this
}

/**
 * 在activity中初始化ViewPager2
 */
fun ViewPager2.initMyCourseActivity(
    activity: FragmentActivity,
    fragments: ArrayList<Fragment>,
    isUserInputEnabled: Boolean = true,
): ViewPager2 {
    // 是否可滑动
    this.isUserInputEnabled = isUserInputEnabled
    this.isSaveEnabled = false
    // 设置适配器
    adapter = object : FragmentStateAdapter(activity) {
        override fun createFragment(position: Int) = fragments[position]
        override fun getItemCount() = fragments.size
    }
    return this
}

/**
 * 在fragment中初始化ViewPager2
 */
fun ViewPager2.initFragment(
    fragment: Fragment,
    fragments: ArrayList<Fragment>,
    isUserInputEnabled: Boolean = true,
): ViewPager2 {
    // 是否可滑动
    this.isUserInputEnabled = isUserInputEnabled
    // 设置适配器
    adapter = object : FragmentStateAdapter(fragment) {
        override fun createFragment(position: Int) = fragments[position]
        override fun getItemCount() = fragments.size
    }
    return this
}

/**
 * 在fragment中初始化ViewPager
 */
//fun ViewPager.initFragment(
//    fragment: Fragment,
//    fragments: ArrayList<Fragment>,
//): ViewPager {
//    // 设置适配器
//    adapter = object : FragmentStateAdapter(fragment) {
//        override fun createFragment(position: Int) = fragments[position]
//        override fun getItemCount() = fragments.size
//    }
//    return this
//}

/**
 * 在Fragment中初始化ViewPager
 */
fun ViewPager.refreshFragment(
    fragment: Fragment,
    fragments: ArrayList<Fragment>,
): ViewPager {
    // 设置适配器
    adapter = object : FragmentStatePagerAdapter(fragment.childFragmentManager) {
        override fun getCount(): Int {
            return fragments.size
        }

        override fun getItem(position: Int): Fragment {
            return fragments[position]
        }

        override fun getItemPosition(`object`: Any): Int {
            return POSITION_NONE
        }
    }
    return this
}


/**
 * 在Fragment中初始化ViewPager
 */
fun ViewPager.refreshActivity(
    activity: AppCompatActivity,
    fragments: ArrayList<Fragment>,
): ViewPager {
    // 设置适配器
    adapter = object : FragmentStatePagerAdapter(activity.supportFragmentManager) {
        override fun getCount(): Int {
            return fragments.size
        }

        override fun getItem(position: Int): Fragment {
            return fragments[position]
        }

        override fun getItemPosition(`object`: Any): Int {
            return POSITION_NONE
        }
    }
    return this
}


fun ViewPager2.init(
    fragment: Fragment,
    fragments: ArrayList<Fragment>,
    isUserInputEnabled: Boolean = true,
): ViewPager2 {
    //是否可滑动
    this.isUserInputEnabled = isUserInputEnabled
    //设置适配器
    adapter = object : FragmentStateAdapter(fragment) {
        override fun createFragment(position: Int) = fragments[position]
        override fun getItemCount() = fragments.size
    }
    return this
}


/**
 * 绑定普通的Recyclerview
 */
fun RecyclerView.init(
    layoutManger: RecyclerView.LayoutManager,
    bindAdapter: RecyclerView.Adapter<*>,
    isScroll: Boolean = true,
): RecyclerView {
    layoutManager = layoutManger
    setHasFixedSize(true)
    adapter = bindAdapter
    isNestedScrollingEnabled = isScroll
    return this
}


/**
 * SwipeRefreshLayout 刷新
 */
fun SmartRefreshLayout.init(onRefreshListener: () -> Unit) {
    run {
        setOnRefreshListener {
            onRefreshListener.invoke()
        }
    }
}

/**
 * SwipeRefreshLayout 加载更多
 */
fun SmartRefreshLayout.initLoadMore(onLoadMoreListener: () -> Unit) {
    run {
        setOnLoadMoreListener {
            onLoadMoreListener.invoke()
        }
    }
}
