package com.xtj.person.dialog

import android.os.Bundle
import android.view.*
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.viewmodel.getVmClazz

/**
 * 作者 : kaitaoyan
 * 时间 : 2021/5/20
 * 描述 :
 */
abstract class BaseBottomSheetDialogFragment<VM : BaseViewModel, VB : ViewDataBinding> : BottomSheetDialogFragment() {

    lateinit var mDataBind: VB

    lateinit var mViewModel: VM

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mDataBind = DataBindingUtil.inflate(inflater, layoutId(), container, false)
        mDataBind.lifecycleOwner = this
        return mDataBind.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mViewModel = createViewModel()
        initView(savedInstanceState)
        initObserver()
        initListener()
        initData()
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val lp = window?.attributes
        lp?.apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.WRAP_CONTENT
            gravity = Gravity.TOP
            dimAmount = 0.6f // 背景透明度设置
            lp?.flags?.let {
                flags = it or WindowManager.LayoutParams.FLAG_DIM_BEHIND
            }

        }
        window?.attributes = lp
    }

    /**
     * 创建viewModel
     */
    private fun createViewModel(): VM {
        return ViewModelProvider(this).get(getVmClazz(this))
    }

    abstract fun layoutId(): Int

    abstract fun initView(savedInstanceState: Bundle?)

    abstract fun initListener()

    abstract fun initData()

    /**
     * 创建观察者
     */
    open fun initObserver() {}

}