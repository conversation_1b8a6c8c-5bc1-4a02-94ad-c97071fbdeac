package com.xtj.person.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import com.blankj.utilcode.util.KeyboardUtils
import com.xtj.person.common.viewmodel.SelectCourseDialogViewModel
import com.xtj.person.databinding.LayoutSelectCourseDialogFragmentBinding
import com.xtj.person.R
import com.xtj.person.activity.FacialCheckInTodayCourseActivity
import com.xtj.person.adapter.RvSelectCourseActivityAdapter
import com.xtj.person.adapter.RvSelectCourseDialogAdapter
import com.xtj.person.common.bean.CourseItem
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.init
import com.xtj.person.common.ext.loadMore
import com.xtj.person.common.ext.visible
import com.xtj.person.common.net.network.NetworkUtil
import com.xtj.person.common.util.OnRvItemClickListener

/**
 * 作者 : kaitaoyan
 * 时间 : 2021/5/20
 * 描述 :
 */
class SelectCourseDialogFragment() :
    BaseBottomSheetDialogFragment<SelectCourseDialogViewModel, LayoutSelectCourseDialogFragmentBinding>(), View.OnClickListener {

    private var pageSize = 10
    private var firstPageNum = 1
    private var pageNum = firstPageNum
    private var rvResultList = ArrayList<CourseItem>()
    private var rvSelectCourseActivityAdapter: RvSelectCourseDialogAdapter?=null
    private var isRefresh: Boolean = true
    private var selectDate:String = ""


    private var isShowSelectAll = true

    interface OnSelectCourseListener{
        fun selectCourse(courseItem: CourseItem)
        fun selectAll()
    }

    var onSelectCourseListener: OnSelectCourseListener?=null


    interface OnDialogDismissListener{
        fun onDismiss()
    }

    var onDialogDismissListener:OnDialogDismissListener?=null

    override fun layoutId(): Int = R.layout.layout_select_course_dialog_fragment



    override fun initView(savedInstanceState: Bundle?) {

        arguments?.let {
            selectDate=   it.getString("activitySelectDate","")
            isShowSelectAll = it.getBoolean("isShowSelectAll",true)
        }
       mDataBind.run {
           ivClose.setOnClickListener {
               dismiss()
           }

           if(isShowSelectAll){
               layoutAll.visible()
           }else{
               layoutAll.gone()
           }

           ivSearch.setOnClickListener(this@SelectCourseDialogFragment)
           layoutAll.setOnClickListener(this@SelectCourseDialogFragment)
           loadData()

           recyclerView.run {
               (itemAnimator as SimpleItemAnimator).supportsChangeAnimations =
                   false
               rvSelectCourseActivityAdapter =
                   RvSelectCourseDialogAdapter(rvResultList)
               layoutManager = LinearLayoutManager(context)
               adapter = rvSelectCourseActivityAdapter
                rvSelectCourseActivityAdapter?.onItemClickListener = object : OnRvItemClickListener{
                    override fun onClickItem(position: Int) {
                        if(rvResultList.size>position){
                            onSelectCourseListener?.selectCourse(rvResultList[position])
                            dismiss()
                        }

                    }

                }

           }
           if(!NetworkUtil.isNetworkAvailable(context)){
               netErrorGroup.visible()
           }else{
               netErrorGroup.gone()
           }

       }




    }

    override fun initObserver() {
        super.initObserver()
        mViewModel.selectCourseListLiveData.observe (this){
            if(it.code==200){
                mDataBind.netErrorGroup.gone()
                mDataBind.smartRefreshLayout.finishRefresh()
                mDataBind.smartRefreshLayout.finishLoadMore()
                if (it.data.list.isNullOrEmpty() && rvResultList.isNullOrEmpty()) {
                    //显示空布局
                    showHideEmpty(true)
                    mDataBind.smartRefreshLayout.setEnableLoadMore(false)
                } else {
                    showHideEmpty(false)
                    if (isRefresh) {
                        //下拉刷新的情况或者第一次加载
                        rvResultList.clear()
                    }
                    rvResultList.addAll(it.data.list)



                    rvSelectCourseActivityAdapter?.notifyDataSetChanged()



                }

                if (rvResultList.size == it.data.count && it.data.count > 0) {
                    mDataBind.tvNoMore.visible()
                    mDataBind.smartRefreshLayout.setEnableLoadMore(false)
                } else {
                    mDataBind.tvNoMore.gone()
                }


            }
        }
    }



    private fun refreshData(){
        mDataBind.smartRefreshLayout.setEnableLoadMore(true)
        isRefresh = true
        pageNum = firstPageNum
        mViewModel.getSelectCourseList(
            getSearchKeyWords(),
            pageSize,
            pageNum,
            selectDate
        )
    }

    private fun loadData() {
        isRefresh = true
        mViewModel.getSelectCourseList(getSearchKeyWords(), pageSize, pageNum,selectDate)


        mDataBind.smartRefreshLayout.init {

            refreshData()
        }

        mDataBind.smartRefreshLayout.loadMore {

            isRefresh = false
            mViewModel.getSelectCourseList(getSearchKeyWords(),
                pageSize,
                ++pageNum,selectDate
            )
        }


    }


    private fun showHideEmpty(isShowEmpty: Boolean) {
        mDataBind.run {
            if (isShowEmpty) {
                recyclerView.gone()
                emptyGroup.visible()
            } else {
                recyclerView.visible()
                emptyGroup.gone()
            }
        }

    }


    override fun initListener() {


    }

    override fun onDismiss(dialog: DialogInterface) {
        onDialogDismissListener?.onDismiss()
        super.onDismiss(dialog)
    }




    override fun onStart() {
        super.onStart()
        (dialog as? com.google.android.material.bottomsheet.BottomSheetDialog)?.behavior?.isDraggable = false
        val window = dialog?.window
        val lp = window?.attributes
        lp?.apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.WRAP_CONTENT
            gravity = Gravity.BOTTOM
            dimAmount = 0.6f // 背景透明度设置
            this.flags.let {
                flags = it or WindowManager.LayoutParams.FLAG_DIM_BEHIND
            }
        }
        window?.attributes = lp
    }






    override fun initData() {

    }
    private fun getSearchKeyWords():String{
        return mDataBind.etCourseName.text.toString()
    }


    override fun onClick(view: View?) {
        mDataBind.run {
            when(view){
                ivSearch->{
                    KeyboardUtils.hideSoftInput(mDataBind.etCourseName)
                    rvResultList.clear()
                    refreshData()

                }

                layoutAll->{
                    onSelectCourseListener?.selectAll()
                    dismiss()
                }

                else->{}
            }
        }
    }

    companion object {
        fun newInstance(activitySelectDate:String,isShowSelectAll:Boolean) =
            SelectCourseDialogFragment().apply {
                arguments = Bundle().apply {
                    putString("activitySelectDate",activitySelectDate)
                    putBoolean("isShowSelectAll",isShowSelectAll)
                }
                setStyle(STYLE_NORMAL, R.style.DownTopDialogTheme)
                isCancelable = true
            }
    }
}

