package com.xtj.person.common.util

import java.text.SimpleDateFormat
import java.util.*

object DateUtils {

    /**
     * 获取今天日期，格式：9月9日 星期二
     */
    fun getTodayDateStr(): String {
        return getDefaultFormatDate(Date())
    }

    /**
     * 获取指定日期的字符串，格式：9月9日 星期二
     */
    fun getDateStr(date: Date): String {
        val calendar = Calendar.getInstance().apply { time = date }

        // 格式化月日
        val dateFormat = SimpleDateFormat("M月d日", Locale.CHINA)
        val dateStr = dateFormat.format(date)

        // 获取星期
        val weekDays = arrayOf("星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六")
        val week = weekDays[calendar.get(Calendar.DAY_OF_WEEK) - 1]

        return "$dateStr $week"
    }

    fun getTodayDate(): String {
        return getDefaultFormatDate(Date()) // 返回类似 "2025-09-19"
    }

    fun getDefaultFormatDate(date: Date): String {
        val formatter = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return formatter.format(date)
    }

}
