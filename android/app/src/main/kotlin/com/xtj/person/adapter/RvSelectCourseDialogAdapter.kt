package com.xtj.person.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.xtj.person.common.bean.CourseItem
import com.xtj.person.common.bean.SchoolListItem
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.visible
import com.xtj.person.common.util.OnRvItemClickListener
import com.xtj.person.databinding.LayoutRvSelectCourseItemBinding
import com.xtj.person.databinding.LayoutRvSelectLocationItemBinding
import com.xtj.person.databinding.LayoutSelectLocationDialogFragmentBinding

class RvSelectCourseDialogAdapter(
    var beans: ArrayList<CourseItem>
) :
    RecyclerView.Adapter<RvSelectCourseDialogAdapter.ViewHolder>() {
         var selectPosition = -1
    var onItemClickListener: OnRvItemClickListener? = null


    class ViewHolder(
        var layoutRvSelectCourseItemBinding:
        LayoutRvSelectCourseItemBinding
    ) :
        RecyclerView.ViewHolder(layoutRvSelectCourseItemBinding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutRvSelectCourseItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }



    override fun getItemCount(): Int {
        return beans.size

    }


    @SuppressLint("SuspiciousIndentation")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        holder.layoutRvSelectCourseItemBinding.run {
            beans[holder.adapterPosition].run {
                if(selectPosition==holder.adapterPosition){
                    ivSelectStatus.visible()
                }else{
                    ivSelectStatus.gone()
                }


                tvName.text = curriculum_name

                layoutRoot.setOnClickListener {
                    onItemClickListener?.onClickItem(holder.adapterPosition)
                    selectPosition = holder.adapterPosition
                    notifyDataSetChanged()
                }


        }
    }

    }


}