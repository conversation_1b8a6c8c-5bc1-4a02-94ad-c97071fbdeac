package com.xtj.person.common.viewmodel

import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.bean.CourseListBean
import com.xtj.person.common.ext.getSelectSchoolItemBean
import com.xtj.person.common.ext.rxHttpRequest
import com.xtj.person.common.net.repository.DataRepository

class SelectCourseDialogViewModel: BaseViewModel() {

    val selectCourseListLiveData = MutableLiveData<CourseListBean>()


    fun getSelectCourseList(keyword:String,size:Int,page:Int,date:String){


        rxHttpRequest {
            onRequest={
                selectCourseListLiveData.value = DataRepository.getSelectCourseList(keyword,size,page,date).await()

            }

            onError={

            }
        }
    }

}