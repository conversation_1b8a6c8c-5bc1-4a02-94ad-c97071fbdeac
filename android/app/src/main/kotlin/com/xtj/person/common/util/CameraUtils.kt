package com.xtj.person.common.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ImageFormat
import android.graphics.Matrix
import android.graphics.Rect
import android.graphics.RectF
import android.util.Log
import androidx.annotation.OptIn
import androidx.camera.core.AspectRatio
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageProxy
import androidx.camera.view.PreviewView
import com.xtj.person.activity.StartCheckActivity
import dev.steenbakker.mobile_scanner.utils.YuvToRgbConverter
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

object CameraUtils {

     fun yuv420888ToNV21(image: ImageProxy): ByteArray {
        val w = image.width
        val h = image.height
        val nv21 = ByteArray(w * h * 3 / 2)

        val yPlane = image.planes[0]
        val uPlane = image.planes[1]
        val vPlane = image.planes[2]

        // Copy Y plane
        val yBuf = yPlane.buffer
        yBuf.rewind()
        val yRowStride = yPlane.rowStride
        val yPixelStride = yPlane.pixelStride
        var out = 0
        if (yPixelStride == 1 && yRowStride == w) {
            yBuf.get(nv21, 0, w * h)
            out = w * h
        } else {
            val yArr = ByteArray(yBuf.remaining())
            yBuf.get(yArr)
            for (row in 0 until h) {
                var col = 0
                val rowStart = row * yRowStride
                while (col < w) {
                    nv21[out++] = yArr[rowStart + col * yPixelStride]
                    col++
                }
            }
        }

        // Interleave VU for NV21
        val uBuf = uPlane.buffer
        val vBuf = vPlane.buffer
        uBuf.rewind(); vBuf.rewind()
        val uArr = ByteArray(uBuf.remaining())
        val vArr = ByteArray(vBuf.remaining())
        uBuf.get(uArr); vBuf.get(vArr)
        val uRowStride = uPlane.rowStride
        val vRowStride = vPlane.rowStride
        val uPixelStride = uPlane.pixelStride
        val vPixelStride = vPlane.pixelStride
        val uvWidth = w / 2
        val uvHeight = h / 2
        var uvOut = w * h
        for (row in 0 until uvHeight) {
            val uRowStart = row * uRowStride
            val vRowStart = row * vRowStride
            for (col in 0 until uvWidth) {
                val uIndex = uRowStart + col * uPixelStride
                val vIndex = vRowStart + col * vPixelStride
                nv21[uvOut++] = vArr.getOrElse(vIndex) { 0 }
                nv21[uvOut++] = uArr.getOrElse(uIndex) { 0 }
            }
        }

        return nv21
    }

     fun chooseAspectRatio(w: Int, h: Int): Int {
        val previewRatio = max(w, h).toDouble() / min(w, h)
        val diff4by3 = abs(previewRatio - 4.0 / 3.0)
        val diff16by9 = abs(previewRatio - 16.0 / 9.0)
        return if (diff4by3 <= diff16by9) AspectRatio.RATIO_4_3 else AspectRatio.RATIO_16_9
    }

     fun cosine(a: FloatArray, b: FloatArray): Float {
        if (a.size != b.size) return -1f
        var dot=0.0; var na=0.0; var nb=0.0
        for (i in a.indices){ dot += a[i]*b[i]; na += a[i]*a[i]; nb += b[i]*b[i] }
        if (na==0.0 || nb==0.0) return -1f
        return (dot / (Math.sqrt(na)*Math.sqrt(nb))).toFloat()
    }





     fun buildTransformMatrix(
        analysisW: Int,
        analysisH: Int,
        viewW: Int,
        viewH: Int,
        scaleType: PreviewView.ScaleType,
        mirror: Boolean,
    ): Matrix {
        val scaleForFit = minOf(viewW.toFloat() / analysisW, viewH.toFloat() / analysisH)
        val scaleForFill = maxOf(viewW.toFloat() / analysisW, viewH.toFloat() / analysisH)
        val scale = when (scaleType) {
            PreviewView.ScaleType.FIT_CENTER,
            PreviewView.ScaleType.FIT_START,
            PreviewView.ScaleType.FIT_END -> scaleForFit
            PreviewView.ScaleType.FILL_CENTER,
            PreviewView.ScaleType.FILL_START,
            PreviewView.ScaleType.FILL_END -> scaleForFill
        }
        val contentW = analysisW * scale
        val contentH = analysisH * scale
        val offsetX = when (scaleType) {
            PreviewView.ScaleType.FIT_START, PreviewView.ScaleType.FILL_START -> 0f
            PreviewView.ScaleType.FIT_CENTER, PreviewView.ScaleType.FILL_CENTER -> (viewW - contentW) / 2f
            PreviewView.ScaleType.FIT_END, PreviewView.ScaleType.FILL_END -> (viewW - contentW)
        }
        val offsetY = when (scaleType) {
            PreviewView.ScaleType.FIT_START, PreviewView.ScaleType.FILL_START -> 0f
            PreviewView.ScaleType.FIT_CENTER, PreviewView.ScaleType.FILL_CENTER -> (viewH - contentH) / 2f
            PreviewView.ScaleType.FIT_END, PreviewView.ScaleType.FILL_END -> (viewH - contentH)
        }
        val m = Matrix()
        if (mirror) {
            // Mirror around analysis buffer's vertical edge
            m.preTranslate(analysisW.toFloat(), 0f)
            m.preScale(-1f, 1f)
        }
        m.postScale(scale, scale)
        m.postTranslate(offsetX, offsetY)
        return m
    }

    fun transformCoordinates(
        boxes: IntArray,
        frameW: Int,
        frameH: Int,
        frameRotation: Int,
        viewW: Int,
        viewH: Int,
        isFrontCamera: Boolean,
        cachedScaleType: PreviewView.ScaleType
    ): IntArray {
        if (boxes.isEmpty()) return boxes

        val previewWidth = viewW
        val previewHeight = viewH

        if (previewWidth == 0 || previewHeight == 0) return boxes

        // Dimensions of the analysis buffer after rotation applied in native
        val analysisW = if (frameRotation == 90 || frameRotation == 270) frameH else frameW
        val analysisH = if (frameRotation == 90 || frameRotation == 270) frameW else frameH

        // Build a transform matrix once, then map all boxes via 矩阵映射
        val m = buildTransformMatrix(
            analysisW = analysisW,
            analysisH = analysisH,
            viewW = previewWidth,
            viewH = previewHeight,
            scaleType = cachedScaleType,
            mirror = isFrontCamera
        )

        Log.d("DEBUG", "Transform: analysis=${analysisW}x${analysisH}, view=${previewWidth}x${previewHeight}, front=${isFrontCamera}, scaleType=${cachedScaleType}")

        val out = IntArray(boxes.size)
        for (i in 0 until (boxes.size / 4)) {
            val x = boxes[i * 4].toFloat()
            val y = boxes[i * 4 + 1].toFloat()
            val w = boxes[i * 4 + 2].toFloat()
            val h = boxes[i * 4 + 3].toFloat()
            val r = RectF(x, y, x + w, y + h)
            m.mapRect(r)
            out[i * 4] = r.left.toInt()
            out[i * 4 + 1] = r.top.toInt()
            out[i * 4 + 2] = r.width().toInt()
            out[i * 4 + 3] = r.height().toInt()
        }
        return out
    }

    fun isCropValid(bitmap: Bitmap, x: Int, y: Int, width: Int, height: Int): Boolean {
        return x >= 0 && y >= 0 &&
                width > 0 && height > 0 &&
                x + width <= bitmap.width &&
                y + height <= bitmap.height
    }

    fun cropBitmap(bitmap: Bitmap, x: Int, y: Int, width: Int, height: Int): Bitmap? {
        if (!isCropValid(bitmap, x, y, width, height)) {
            return null // 非法区域，返回 null 或抛出异常
        }
        return Bitmap.createBitmap(bitmap, x, y, width, height)
    }

    /**
     * 水平镜像翻转 Bitmap
     * @param bitmap 原始 Bitmap
     * @return 翻转后的 Bitmap（新对象）
     */
    fun mirrorBitmap(bitmap: Bitmap): Bitmap {
        // 创建 Matrix 并设置水平翻转（-1 表示镜像）
        val matrix = Matrix().apply {
            postScale(-1f, 1f) // 水平翻转（x轴取反，y轴不变）
        }

        // 创建翻转后的 Bitmap
        return Bitmap.createBitmap(
            bitmap,          // 原始 Bitmap
            0,               // 起始 x 坐标
            0,               // 起始 y 坐标
            bitmap.width,    // 宽度
            bitmap.height,   // 高度
            matrix,          // 应用变换矩阵
            true             // 是否抗锯齿
        )
    }

    fun expandCropArea(
        x: Int, y: Int, width: Int, height: Int,
        scale: Float,
        bitmapWidth: Int, bitmapHeight: Int
    ): Rect {
        val expandX = (width * scale / 2).toInt()
        val expandY = (height * scale / 2).toInt()

        val newX = (x - expandX).coerceAtLeast(0)
        val newY = (y - expandY).coerceAtLeast(0)
        val newWidth = (width + 2 * expandX).coerceAtMost(bitmapWidth - newX)
        val newHeight = (height + 2 * expandY).coerceAtMost(bitmapHeight - newY)

        return Rect(newX, newY, newX + newWidth, newY + newHeight)
    }

    fun rotateBitmap(bitmap: Bitmap, rotationDegrees: Int): Bitmap {
        if (rotationDegrees == 0) return bitmap

        val matrix = Matrix().apply {
            postRotate(rotationDegrees.toFloat())
        }

        return Bitmap.createBitmap(
            bitmap,
            0,
            0,
            bitmap.width,
            bitmap.height,
            matrix,
            true
        )
    }

    @OptIn(ExperimentalGetImage::class)
    fun imageProxyToBitmap(imageProxy: ImageProxy,context: Context): Bitmap? {
        if (imageProxy.format != ImageFormat.YUV_420_888) {
            return null // 仅处理 YUV 格式
        }

        val bitmap = Bitmap.createBitmap(
            imageProxy.width,
            imageProxy.height,
            Bitmap.Config.ARGB_8888
        )

        val converter = YuvToRgbConverter(context)
        converter.yuvToRgb(imageProxy.image!!, bitmap)

        return bitmap
    }

}