package com.xtj.person.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.xtj.person.common.bean.CourseData
import com.xtj.person.common.bean.CourseItem
import com.xtj.person.common.bean.SchoolListItem
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.visible
import com.xtj.person.common.util.OnRvItemClickListener
import com.xtj.person.databinding.LayoutRvSelectCourseItemForActivityBinding
import com.xtj.person.databinding.LayoutRvSelectLocationItemBinding
import com.xtj.person.databinding.LayoutSelectLocationDialogFragmentBinding

class RvSelectCourseActivityAdapter(
    var beans: ArrayList<CourseItem>
) :
    RecyclerView.Adapter<RvSelectCourseActivityAdapter.ViewHolder>() {
         var selectPosition = -1
    var onItemClickListener: OnRvItemClickListener? = null


    class ViewHolder(
        var layoutRvSelectCourseItemForActivityBinding:
        LayoutRvSelectCourseItemForActivityBinding
    ) :
        RecyclerView.ViewHolder(layoutRvSelectCourseItemForActivityBinding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutRvSelectCourseItemForActivityBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }



    override fun getItemCount(): Int {
        return beans.size

    }


    @SuppressLint("SuspiciousIndentation")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        holder.layoutRvSelectCourseItemForActivityBinding.run {
            beans[holder.adapterPosition].run {

                tvItemCourseName.text = curriculum_name
              tvItemClassRoomName.text = classroom_name
            tvItemDuringDate.text = "$start_date-$end_date"

                tvToCheckIn.setOnClickListener {
                    onItemClickListener?.onClickItem(position)
                }

        }
    }

    }


}