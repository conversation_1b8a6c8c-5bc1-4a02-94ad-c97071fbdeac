package com.xtj.person.common.viewmodel

import androidx.lifecycle.MutableLiveData
import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.bean.QueryUserInfoByPhoneBean
import com.xtj.person.common.ext.rxHttpRequest
import com.xtj.person.common.net.repository.DataRepository

class QueryUserByPhoneViewModel: BaseViewModel() {
    val queryUserInfoByPhoneBeanLiveData = MutableLiveData<QueryUserInfoByPhoneBean>()

    fun queryUserInfoByPhone(phoneNum:String){
        rxHttpRequest {
            onRequest={
                queryUserInfoByPhoneBeanLiveData.value = DataRepository.queryUserInfoByPhone(phoneNum).await()
            }

            onError={


            }
        }

    }


}