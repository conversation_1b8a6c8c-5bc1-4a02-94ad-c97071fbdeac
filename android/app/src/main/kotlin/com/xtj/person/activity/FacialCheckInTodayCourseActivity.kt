package com.xtj.person.activity

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import com.blankj.utilcode.util.ToastUtils
import com.github.gzuliyujiang.calendarpicker.CalendarPicker
import com.github.gzuliyujiang.calendarpicker.OnSingleDatePickListener
import com.xtj.person.adapter.RvSelectCourseActivityAdapter
import com.xtj.person.common.bean.CourseItem
import com.xtj.person.common.bean.SchoolListItem
import com.xtj.person.common.ext.getSelectSchoolItemBean
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.init
import com.xtj.person.common.ext.loadMore
import com.xtj.person.common.ext.updateSelectSchoolItemBean
import com.xtj.person.common.ext.visible
import com.xtj.person.common.net.network.NetworkUtil
import com.xtj.person.common.util.DateUtils
import com.xtj.person.common.util.OnRvItemClickListener
import com.xtj.person.common.viewmodel.FacialCheckInTodayCourseViewModel
import com.xtj.person.databinding.ActivityFacialCheckInTodayCourseLayoutBinding
import com.xtj.person.dialog.NormalMessageDialogFragment
import com.xtj.person.dialog.SelectCourseDialogFragment
import com.xtj.person.dialog.SelectCourseDialogFragment.OnSelectCourseListener
import com.xtj.person.dialog.SelectLocationDialogFragment
import com.xtj.person.dialog.SelectLocationDialogFragment.OnSelectSchoolListener
import java.util.Date


class FacialCheckInTodayCourseActivity: BaseVmActivity<FacialCheckInTodayCourseViewModel, ActivityFacialCheckInTodayCourseLayoutBinding>(),
    View.OnClickListener{
    private var pageSize = 10
    private var firstPageNum = 1
    private var pageNum = firstPageNum
    private var rvResultList = ArrayList<CourseItem>()
    private var rvSelectCourseActivityAdapter: RvSelectCourseActivityAdapter?=null
    private var isRefresh: Boolean = true
    private var selectDate:String = ""
    private var keyWords:String = ""

    override fun getViewBinding(inflater: LayoutInflater): ActivityFacialCheckInTodayCourseLayoutBinding {
        return ActivityFacialCheckInTodayCourseLayoutBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {
        subBinding.run {
            include.title.text = "今日课程"
            include.backBtn.setOnClickListener { finish() }
            tvDate.text = DateUtils.getTodayDateStr()

            tvLoc.setOnClickListener(this@FacialCheckInTodayCourseActivity)
            tvSelectCourse.setOnClickListener(this@FacialCheckInTodayCourseActivity)
            tvDate.setOnClickListener(this@FacialCheckInTodayCourseActivity)
            ivDate.setOnClickListener(this@FacialCheckInTodayCourseActivity)

            val selectSchoolBean = getSelectSchoolItemBean()
            if(selectSchoolBean==null){
                tvLoc.text = "请选择校区"


                val normalMessageDialog =
                    NormalMessageDialogFragment.newInstance(
                        "请选择校区"
                    )
                normalMessageDialog.show(
                    supportFragmentManager,
                    ""
                )
                normalMessageDialog.onClickSureListener =
                    object :
                        NormalMessageDialogFragment.OnClickSureListener {
                        override fun clickSure() {
                         selectSchool()

                        }
                    }





            }else{
                tvLoc.text = selectSchoolBean.name
            }

            selectDate = DateUtils.getTodayDateStr()

            loadData()

            recyclerView.run {
                (itemAnimator as SimpleItemAnimator).supportsChangeAnimations =
                    false
                rvSelectCourseActivityAdapter =
                    RvSelectCourseActivityAdapter(rvResultList)
                layoutManager = LinearLayoutManager(context)
                adapter = rvSelectCourseActivityAdapter
                rvSelectCourseActivityAdapter?.onItemClickListener = object : OnRvItemClickListener{
                    override fun onClickItem(position: Int) {
                        if(rvResultList.size>position){
                           var intent = Intent(this@FacialCheckInTodayCourseActivity,
                               CheckListActivity::class.java)
                            intent.putExtra("curriculumId",rvResultList[position].curriculum_id)
                            intent.putExtra("curriculumName",rvResultList[position].curriculum_name)
                            startActivity(intent)
                        }

                    }

                }

            }
            if(!NetworkUtil.isNetworkAvailable(this@FacialCheckInTodayCourseActivity)){
                netErrorGroup.visible()
            }else{
                netErrorGroup.gone()
            }
        }

    }

    override fun initObserver() {
        super.initObserver()
        mViewModel.selectCourseListLiveData.observe (this){
            if(it.code==200){
                subBinding.netErrorGroup.gone()
                subBinding.smartRefreshLayout.finishRefresh()
                subBinding.smartRefreshLayout.finishLoadMore()
                if (it.data.list.isNullOrEmpty() && rvResultList.isNullOrEmpty()) {
                    //显示空布局
                    showHideEmpty(true)
                    subBinding.smartRefreshLayout.setEnableLoadMore(false)
                } else {
                    showHideEmpty(false)
                    if (isRefresh) {
                        //下拉刷新的情况或者第一次加载
                        rvResultList.clear()
                    }
                    rvResultList.addAll(it.data.list)



                    rvSelectCourseActivityAdapter?.notifyDataSetChanged()



                }

                if (rvResultList.size == it.data.count && it.data.count > 0) {
                    subBinding.tvNoMore.visible()
                    subBinding.smartRefreshLayout.setEnableLoadMore(false)
                } else {
                    subBinding.tvNoMore.gone()
                }


            }
        }
    }



    private fun refreshData(){
        subBinding.smartRefreshLayout.setEnableLoadMore(true)
        isRefresh = true
        pageNum = firstPageNum
        mViewModel.getSelectCourseList(
            keyWords,
            pageSize,
            pageNum,
            selectDate
        )
    }

    private fun loadData() {
        isRefresh = true
        mViewModel.getSelectCourseList(keyWords, pageSize, pageNum,selectDate)


        subBinding.smartRefreshLayout.init {

            refreshData()
        }

        subBinding.smartRefreshLayout.loadMore {

            isRefresh = false
            mViewModel.getSelectCourseList(keyWords,
                pageSize,
                ++pageNum,selectDate
            )
        }


    }


    private fun showHideEmpty(isShowEmpty: Boolean) {
        subBinding.run {
            if (isShowEmpty) {
                recyclerView.gone()
                emptyGroup.visible()
            } else {
                recyclerView.visible()
                emptyGroup.gone()
            }
        }

    }



    private fun selectSchool(){
        val selectLocationDialogFragment = SelectLocationDialogFragment.newInstance()
        selectLocationDialogFragment.show(supportFragmentManager,"")
        selectLocationDialogFragment.onSelectSchoolListener = object:OnSelectSchoolListener{
            override fun selectSchool(schoolBean: SchoolListItem) {
                subBinding.tvLoc.text = schoolBean.name
                updateSelectSchoolItemBean(schoolBean)
                refreshData()
            }

        }
    }


    override fun onClick(v: View?) {
        subBinding.run {
            when(v){


                tvLoc->{
                    selectSchool()
                }
                tvSelectCourse->{
                    val schoolId =  getSelectSchoolItemBean()?.id?:-1
                    if(schoolId==-1){
                        ToastUtils.showShort("请先选择校区")
                        return
                    }
                    val selectCourseDialogFragment = SelectCourseDialogFragment.newInstance(selectDate,true)
                    selectCourseDialogFragment.show(supportFragmentManager,"")

                    selectCourseDialogFragment.onSelectCourseListener = object:OnSelectCourseListener{
                        override fun selectCourse(courseItem: CourseItem) {
                            rvResultList.clear()
                            rvResultList.add(courseItem)
                            rvSelectCourseActivityAdapter?.notifyDataSetChanged()
                            tvSelectCourse.text = courseItem.curriculum_name

                        }

                        override fun selectAll() {
                            refreshData()
                            tvSelectCourse.text = "全部课程"
                        }

                    }


                }

                tvDate,ivDate->{
                    val picker = CalendarPicker(this@FacialCheckInTodayCourseActivity)
//                    picker.setRangeDateOnFuture(3)
                    picker.enablePagerSnap()

                      val  singleTimeInMillis = System.currentTimeMillis()

                    picker.setSelectedDate(singleTimeInMillis)
                    picker.setOnSingleDatePickListener(object : OnSingleDatePickListener {


                        override fun onSingleDatePicked(date: Date) {
                            tvDate.text =  DateUtils.getDateStr(date)
                            selectDate =  DateUtils.getDefaultFormatDate(date)
                            refreshData()

                        }

                        override fun onMonthChanged(date: Date) {

                        }
                    })
                    picker.show()
                }

            }
        }

    }
}