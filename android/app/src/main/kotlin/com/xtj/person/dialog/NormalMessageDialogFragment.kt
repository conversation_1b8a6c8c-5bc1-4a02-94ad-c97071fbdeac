package com.xtj.person.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import com.xtj.person.R
import com.xtj.person.common.viewmodel.EmptyViewModel
import com.xtj.person.databinding.LayoutNormalMessageDialogFragmentBinding

/**
 * 作者 : kaitaoyan
 * 时间 : 2021/6/1
 * 描述 :
 */
class NormalMessageDialogFragment :
    BaseDialogFragment<EmptyViewModel, LayoutNormalMessageDialogFragmentBinding>() {

    interface OnClickSureListener{
        fun clickSure()
    }

    var onClickSureListener:OnClickSureListener?=null

    interface OnDismissListener{
        fun onDismiss()
    }

    var onDismissListener:OnDismissListener?=null


    override fun layoutId(): Int = R.layout.layout_normal_message_dialog_fragment

    override fun initView(savedInstanceState: Bundle?) {
        mDataBind.click = ProxyClick()
        arguments?.let {
         mDataBind.tvMessage.text=   it.getString("message","")
        }
    }

    override fun initListener() {
    }

    override fun initData() {
    }

    override fun onStart() {
        super.onStart()
        val window = dialog?.window
        val lp = window?.attributes
        lp?.apply {
            width = WindowManager.LayoutParams.WRAP_CONTENT
            height = WindowManager.LayoutParams.WRAP_CONTENT
            gravity = Gravity.CENTER
            dimAmount = 0.6f // 背景透明度设置
            this.flags.let {
                flags = it or WindowManager.LayoutParams.FLAG_DIM_BEHIND
            }
        }
        window?.attributes = lp
    }

    override fun onDismiss(dialog: DialogInterface) {
        onDismissListener?.onDismiss()
        super.onDismiss(dialog)

    }

    inner class ProxyClick {
        fun dismissBtn() {
            dismiss()
        }

        fun clickSure() {
            onClickSureListener?.clickSure()
            dismiss()
        }
    }

    companion object {
        fun newInstance(message:String,isCancelAble:Boolean = true) =
            NormalMessageDialogFragment().apply {
                arguments = Bundle().apply {
                    putString("message",message)
                }
                setStyle(STYLE_NORMAL, R.style.DownTopDialogTheme)
                isCancelable = isCancelAble
            }
    }

}