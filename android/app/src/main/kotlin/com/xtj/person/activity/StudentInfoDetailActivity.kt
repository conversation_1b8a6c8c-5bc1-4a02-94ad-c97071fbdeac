package com.xtj.person.activity

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import com.xtj.person.activity.QueryUserByPhoneActivity
import com.xtj.person.common.util.ImageViewUtils
import com.xtj.person.common.viewmodel.StudentInfoDetailViewModel
import com.xtj.person.databinding.ActivityStudentInfoDetailBinding

class StudentInfoDetailActivity: BaseVmActivity<StudentInfoDetailViewModel, ActivityStudentInfoDetailBinding>() {
    var userName = ""
    var idNumber = ""
    var guid = ""
    var icon = ""
    var phoneNum = ""
    override fun getViewBinding(inflater: LayoutInflater): ActivityStudentInfoDetailBinding {

        return ActivityStudentInfoDetailBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {

        userName =  intent.getStringExtra("userName" )?:""
        idNumber =  intent.getStringExtra("idNumber" )?:""
        guid =  intent.getStringExtra("guid" )?:""
        icon =  intent.getStringExtra("icon" )?:""
        phoneNum =  intent.getStringExtra("phoneNum" )?:""
        subBinding.run {
            include.title.text = "录入学员信息"
            include.backBtn.setOnClickListener { finish() }
            include.rightBtn.text = "编辑信息"
            include.rightBtn.setOnClickListener {
                var intent = Intent(this@StudentInfoDetailActivity, StudentInfoEditActivity::class.java)
                intent.putExtra("userName",userName)
                intent.putExtra("idNumber",idNumber)
                intent.putExtra("guid",guid)
                intent.putExtra("icon",icon)
                intent.putExtra("phoneNum",phoneNum)

                startActivity(intent)
                finish()
            }

            tvName.text = userName
            tvIdCard.text = idNumber
            tvId.text = guid
            ImageViewUtils.loadImg(icon,ivUserImg)


        }
    }
}