package com.xtj.person.common.viewmodel

import androidx.lifecycle.MutableLiveData
import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.bean.NewestCheckInResultBean
import com.xtj.person.common.ext.rxHttpRequest
import com.xtj.person.common.net.repository.DataRepository

class StartCheckViewModel: BaseViewModel() {

    var newestCheckInResultLiveData = MutableLiveData<NewestCheckInResultBean>()


    fun getNewestCheckInResult(curriculumId:Int,period:String){
        rxHttpRequest {
            onRequest={
                newestCheckInResultLiveData.value = DataRepository.getNewestCheckInResult(curriculumId,period).await()
            }

            onError={

            }
        }

    }

}