package com.xtj.person.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import com.blankj.utilcode.util.KeyboardUtils
import com.xtj.person.R
import com.xtj.person.adapter.RvSelectLocationAdapter
import com.xtj.person.common.bean.SchoolListItem
import com.xtj.person.common.ext.getSelectSchoolItemBean
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.init
import com.xtj.person.common.ext.loadMore
import com.xtj.person.common.ext.visible
import com.xtj.person.common.net.network.NetworkUtil
import com.xtj.person.common.util.OnRvItemClickListener
import com.xtj.person.common.viewmodel.SelectLocationDialogViewModel
import com.xtj.person.databinding.LayoutSelectLocationDialogFragmentBinding

/**
 * 作者 : kaitaoyan
 * 时间 : 2021/5/20
 * 描述 :
 */
class SelectLocationDialogFragment :
    BaseBottomSheetDialogFragment<SelectLocationDialogViewModel, LayoutSelectLocationDialogFragmentBinding>(), View.OnClickListener {

    private var pageSize = 10
    private var firstPageNum = 1
    private var pageNum = firstPageNum
    private var rvResultList = ArrayList<SchoolListItem>()
    private var rvSelectLocationAdapter: RvSelectLocationAdapter?=null
    private var isRefresh: Boolean = true
    interface OnSelectSchoolListener{
        fun selectSchool(schoolBean:SchoolListItem)
    }

    var onSelectSchoolListener: OnSelectSchoolListener?=null


    interface OnDialogDismissListener{
        fun onDismiss()
    }

    var onDialogDismissListener:OnDialogDismissListener?=null

    override fun layoutId(): Int = R.layout.layout_select_location_dialog_fragment



    override fun initView(savedInstanceState: Bundle?) {
        mDataBind.run {
            ivClose.setOnClickListener {
                dismiss()
            }

            ivSearch.setOnClickListener(this@SelectLocationDialogFragment)
        }


        loadData()

        mDataBind.recyclerView.run {
            (itemAnimator as SimpleItemAnimator).supportsChangeAnimations =
                false
            rvSelectLocationAdapter =
                RvSelectLocationAdapter(rvResultList)
            layoutManager = LinearLayoutManager(context)
            adapter = rvSelectLocationAdapter
            rvSelectLocationAdapter?.onItemClickListener = object : OnRvItemClickListener{
                override fun onClickItem(position: Int) {
                    if(rvResultList.size>position){
                        onSelectSchoolListener?.selectSchool(rvResultList[position])
                        dismiss()
                    }

                }

            }

        }
        if(!NetworkUtil.isNetworkAvailable(context)){
            mDataBind.netErrorGroup.visible()
        }else{
            mDataBind.netErrorGroup.gone()
        }



    }

    override fun initObserver() {
        super.initObserver()
        mViewModel.schoolListBeanLiveData.observe (this){
            if(it.code==200){
                mDataBind.netErrorGroup.gone()
                mDataBind.smartRefreshLayout.finishRefresh()
                mDataBind.smartRefreshLayout.finishLoadMore()
                if (it.data.list.isNullOrEmpty() && rvResultList.isNullOrEmpty()) {
                    //显示空布局
                    showHideEmpty(true)
                    mDataBind.smartRefreshLayout.setEnableLoadMore(false)
                } else {
                    showHideEmpty(false)
                    if (isRefresh) {
                        //下拉刷新的情况或者第一次加载
                        rvResultList.clear()
                    }
                    rvResultList.addAll(it.data.list)

                    getSelectSchoolItemBean()?.let { selectSchoolBean->
                        val selectIndex =  rvResultList.indexOfFirst { it.id==selectSchoolBean.id}
                        rvSelectLocationAdapter?.selectPosition = selectIndex
                    }


                    rvSelectLocationAdapter?.notifyDataSetChanged()



                }

                if (rvResultList.size == it.data.count && it.data.count > 0) {
                    mDataBind.tvNoMore.visible()
                    mDataBind.smartRefreshLayout.setEnableLoadMore(false)
                } else {
                    mDataBind.tvNoMore.gone()
                }


            }
        }
    }

    override fun initListener() {


    }

    override fun onDismiss(dialog: DialogInterface) {
        onDialogDismissListener?.onDismiss()
        super.onDismiss(dialog)
    }




    override fun onStart() {
        super.onStart()
        (dialog as? com.google.android.material.bottomsheet.BottomSheetDialog)?.behavior?.isDraggable = false

        val window = dialog?.window
        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        val lp = window?.attributes
        lp?.apply {
            width = WindowManager.LayoutParams.MATCH_PARENT
            height = WindowManager.LayoutParams.WRAP_CONTENT
            gravity = Gravity.BOTTOM
            dimAmount = 0.6f // 背景透明度设置
            this.flags.let {
                flags = it or WindowManager.LayoutParams.FLAG_DIM_BEHIND
            }
        }
        window?.attributes = lp
    }






    override fun initData() {

    }



    override fun onClick(view: View?) {
        mDataBind.run {
            when(view){
                ivSearch->{
                    KeyboardUtils.hideSoftInput(mDataBind.etCourseName)
                    rvResultList.clear()
                    refreshData()

                }

            }
        }
    }

    companion object {
        fun newInstance() =
            SelectLocationDialogFragment().apply {
                arguments = Bundle().apply {
                }
                setStyle(STYLE_NORMAL, R.style.DownTopDialogTheme)
                isCancelable = true
            }
    }


    private fun refreshData(){
        mDataBind.smartRefreshLayout.setEnableLoadMore(true)
        isRefresh = true
        pageNum = firstPageNum
        mViewModel.getSchoolList(
            getSearchKeyWords(),
            pageSize,
            pageNum
        )
    }

    private fun loadData() {
        isRefresh = true
        mViewModel.getSchoolList(getSearchKeyWords(), pageSize, pageNum)


        mDataBind.smartRefreshLayout.init {

            cancelSelected()
            refreshData()
        }

        mDataBind.smartRefreshLayout.loadMore {
            cancelSelected()
            isRefresh = false
            mViewModel.getSchoolList(getSearchKeyWords(),
                pageSize,
                ++pageNum
            )
        }


    }


    private fun showHideEmpty(isShowEmpty: Boolean) {
        mDataBind.run {
            if (isShowEmpty) {
                recyclerView.gone()
                emptyGroup.visible()
            } else {
                recyclerView.visible()
                emptyGroup.gone()
            }
        }

    }


    private fun getSearchKeyWords():String{
        return mDataBind.etCourseName.text.toString()
    }

    private fun cancelSelected(){

    }


}

