package com.xtj.person.common.viewmodel

import androidx.lifecycle.MutableLiveData
import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.bean.ChangeStudentInfoResultBean
import com.xtj.person.common.ext.rxHttpRequest
import com.xtj.person.common.net.repository.DataRepository

class StudentInfoEditViewModel: BaseViewModel() {

    val changeUserInfoResultLiveData = MutableLiveData<ChangeStudentInfoResultBean>()
    val changeUserInfoFailLiveData = MutableLiveData<Boolean>()

    fun changeUserInfo(guid:String,userName:String,idNumber:String,phoneNum:String,iconPath:String){
        rxHttpRequest {
            onRequest={
                changeUserInfoResultLiveData.value = DataRepository.changeUserInfo(guid,userName,idNumber,phoneNum,"").await()
            }

            onError={
                changeUserInfoFailLiveData.value = true
            }
        }
    }
}