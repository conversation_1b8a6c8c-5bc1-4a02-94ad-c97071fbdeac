package com.xtj.person.common.ext

import android.text.TextUtils
import androidx.annotation.NonNull
import com.blankj.utilcode.util.AppUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tencent.mmkv.MMKV
import com.xtj.person.common.bean.SchoolListItem
import java.util.Date


const val PDF_BG_MODE_WHITE = 236
const val PDF_BG_MODE_BLACK = 237
const val PDF_BG_MODE_GREEN = 238
/**
 * 获取MMKV
 */
val mmkv: MMKV by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    MMKV.mmkvWithID(AppUtils.getAppPackageName())!!
}



fun getMobile():String{
    return mmkv.decodeString("mobile")?:""
}

fun setMobile(mobile:String){
    mmkv.encode("mobile",mobile)
}

fun setLoginModeIsProduct(isProduct:Boolean){
    mmkv.encode("LoginModeIsProduct",isProduct)
}
fun getLoginModeIsProduct(): Boolean{
    return mmkv.decodeBool("LoginModeIsProduct",true)
}

fun setLoginToken(token:String){
    mmkv.encode("LoginToken",token)
}

/**
 * 测试用的token
 */
fun getLoginToken():String{
    return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJndWlkIjoiZDJvZ21xbzFyNWczc2p0Mm4xcGciLCJhcHBfY29kZSI6MTYxLCJjbGllbnRfaWQiOiIiLCJyZWZyZXNoX3Rva2VuIjpmYWxzZSwiZXhwIjoxNzU4ODY3NDk2MDAwLCJpc3MiOiJ1c2VyX2NlbnRlciJ9.ZqC1CetQX1xlHffdg7Nd9G_CKAeGoelpJZ5aRy11RGU"
//    return mmkv.decodeString("LoginToken")?:""
}



/**
 * 设置是否已经登录
 */
fun setIsLogin(isLogin: Boolean) {
    mmkv.encode("login", isLogin)
}

/**
 * 是否已经登录
 */
fun isLogin(): Boolean {
    return mmkv.decodeBool("login", false)
}


/**
 * 通知权限设置打开时间
 */
fun setPermissionShowNum(num: Long) {
    mmkv.encode("permission_show_num", num)
}

/**
 * 获取权限设置打开时间
 */
fun getPermissionShowNum(): Long {
    return mmkv.decodeLong("permission_show_num", 0)
}


/**
 * 邀好友，得积分
 */
fun setInviteFriends(isLogin: Boolean) {
    mmkv.encode("invite_friends", isLogin)
}

/**
 * 邀好友，得积分
 */
fun isInviteFriends(): Boolean {
    return mmkv.decodeBool("invite_friends", false)
}

/**
 * 好友引导
 */
fun setFriendsGuide(isLogin: Boolean) {
    mmkv.encode("friends_guide", isLogin)
}

/**
 * 好友引导
 */
fun isFriendsGuide(): Boolean {
    return mmkv.decodeBool("friends_guide", false)
}

/**
 * 设置是否显示推荐课程
 */
fun setIsShowRecommend(isLogin: Boolean) {
    mmkv.encode("recommend", isLogin)
}

fun getHandoutBgColorMulti():Int{
    return mmkv.decodeInt("HandoutBgColor", PDF_BG_MODE_WHITE)
}


fun setHandoutBgColorMulti(handoutBgColor:Int){
     mmkv.encode("HandoutBgColor",handoutBgColor)
}

/**
 * 设置是否显示推荐课程
 */
fun isShowRecommend(): Boolean {
    return mmkv.decodeBool("recommend", true)
}


fun setShowJiFen(isLogin: String) {
    mmkv.encode("jifen", isLogin)
}


fun isShowJiFen(): String? {
    return mmkv.decodeString("jifen")
}


/**
 * 是否调整在线播放页面的窗口亮度
 */
fun setOnLineBrightness(isOnLineBrightness: Boolean) {
    mmkv.encode("online_brightness", isOnLineBrightness)
}

/**
 * 是否调整在线播放页面的窗口亮度
 */
fun isOnLineBrightness(): Boolean {
    return mmkv.decodeBool("online_brightness", false)
}


/**
 * 是否显示过登录错账号的提醒
 */
fun hasShowedLoginWrongNumberHint():Boolean{
    return mmkv.decodeBool("hasShowedLoginWrongNumberHint", false)
}


fun setHasShowedLoginWrongNumberHint(hasShowed:Boolean){
    mmkv.encode("hasShowedLoginWrongNumberHint", hasShowed)
}


/**
 * 是否是第一次登陆
 */
fun isFirst(): Boolean {
    return mmkv.decodeBool("first", true)
}

/**
 * 是否是第一次登陆
 */
fun setFirst(first: Boolean): Boolean {
    return mmkv.encode("first", first)
}


/**
 * 获取devicetoken
 */
fun getDeviceToken(): String? {
    return mmkv.decodeString("deviceToken", "")
}

/**
 * 设置devicetoken
 */
fun setDeviceToken(deviceToken: String) {
    mmkv.encode("deviceToken", deviceToken)
}


fun isFirstLand(): Boolean {
    return mmkv.decodeBool("isFirstLand", true)
}


fun setFirstLand(first: Boolean): Boolean {
    setLastGestureShowTimeMillis(Date().time)
    return mmkv.encode("isFirstLand", first)
}

/**
 * 上一次手势遮罩显示的时间
 */
fun setLastGestureShowTimeMillis(lastShowTimeMillis:Long){
    mmkv.encode("lastGestureShowTimeMillis",lastShowTimeMillis)
}


fun getLastGestureShowTimeMillis():Long{

    return mmkv.decodeLong("lastGestureShowTimeMillis",0L)
}


/**
 * 退出并杀死app
 */
fun isExitKillApp(): Boolean {
    return mmkv.decodeBool("exitKillApp", false)
}

/**
 * 退出并杀死app
 */
fun setExitKillApp(boo: Boolean): Boolean {
    return mmkv.encode("exitKillApp", boo)
}

/**
 * 首次安装首页隐私协议弹框是否显示
 */
fun isShow(): Boolean {
    return mmkv.decodeBool("show", true)
}

/**
 * 首次安装首页隐私协议弹框是否显示
 */
fun setShow(show: Boolean): Boolean {
    return mmkv.encode("show", show)
}


/**
 * 首次进入播放页面显示加入群聊弹框
 */
fun isShowJoinQunLiao(): Boolean {
    return mmkv.decodeBool("show_join_qun_liao", true)
}

fun setJoinQunLiao(show: Boolean): Boolean {
    return mmkv.encode("show_join_qun_liao", show)
}


/**
 * 是否要刷新播放页面
 */
fun isRefreshLivePage(): Boolean {
    return mmkv.decodeBool("live", false)
}

fun setRefreshLivePage(boo: Boolean): Boolean {
    return mmkv.encode("live", boo)
}

/**
 * 考试类型是否选择
 */
fun getExamType(): Boolean {
    return mmkv.decodeBool("exam_type", false)
}

/**
 * 设置考试类型
 */
fun setExamType(first: Boolean): Boolean {
    return mmkv.encode("exam_type", first)
}

/**
 * 首页发现新大陆关闭时间戳
 */
fun getDaLuNum(): Long {
    return mmkv.decodeLong("xin_da_lu", 0)
}

/**
 * 首页发现新大陆关闭时间戳
 */
fun setDaLuNum(num: Long) {
    mmkv.encode("xin_da_lu", num)
}

/**
 * 地区是否选择
 */
fun getLocation(): Boolean {
    return mmkv.decodeBool("location", false)
}

/**
 * 设置地区
 */
fun setLocation(first: Boolean): Boolean {
    return mmkv.encode("location", first)
}

/**
 * 设置选择的地区名称
 */
fun setLocationName(str: String) {
    mmkv.encode("locationName", str)
}

/**
 * 获取选择的地区名称
 */
fun getLocationName(): String? {
    return mmkv.decodeString("locationName", "")
}

/**
 * 设置选择的省份id
 */
fun setProvinceId(num: String) {
    mmkv.encode("provinceId", num)
}

/**
 * 获取选择的省份id
 */
fun getProvinceId(): String? {
    return mmkv.decodeString("provinceId", "-1")
}

/**
 * 设置选择的地区id
 */
fun setLocationId(num: String) {
    mmkv.encode("locationId", num)
}

/**
 * 获取选择的地区id
 */
fun getLocationId(): String? {
    return mmkv.decodeString("locationId", "100000")
}

/**
 * 从考试类型页面直接退出
 */
fun isExamTypeExit(): Boolean {
    return mmkv.decodeBool("exam_exit", false)
}

/**
 * 从考试类型页面直接退出
 */
fun setExamExit(first: Boolean): Boolean {
    return mmkv.encode("exam_exit", first)
}


/**
 * 获取同意隐私政策
 */
fun isUmeng(): Boolean {
    return mmkv.decodeBool("umeng", false)
}

/**
 * 设置同意隐私政策
 */
fun setUmeng(first: Boolean): Boolean {
    return mmkv.encode("umeng", first)
}


/**
 * 登录的时候 选择课程类型引导页 是否选择的了课程类型
 */
fun isSelectedExamType(): Boolean {
    return mmkv.decodeBool("selected_exit_type", false)
}


fun setSelectedExamType(first: Boolean): Boolean {
    return mmkv.encode("selected_exit_type", first)
}


/**
 * 从选择地区页面直接退出
 */
fun isLocationExit(): Boolean {
    return mmkv.decodeBool("location_exit", false)
}


/**
 * 从选择地区页面直接退出
 */
fun setLocationExit(first: Boolean): Boolean {
    return mmkv.encode("location_exit", first)
}

/**
 * 第一次安装app，直接关闭页面，再次启动进入主页
 */
fun isToMainPage(): Boolean {
    return mmkv.decodeBool("to_main_page", false)
}

/**
 * 第一次安装app，直接关闭页面，再次启动进入主页
 */
fun setToMainPage(first: Boolean): Boolean {
    return mmkv.encode("to_main_page", first)
}



fun getSelectSchoolItemBean(): SchoolListItem?{
    val schoolItemBeanString = mmkv.decodeString("schoolItemBean")
    if(!TextUtils.isEmpty(schoolItemBeanString)){
        return Gson().fromJson(schoolItemBeanString, object : TypeToken<SchoolListItem>() {}.type)
    }
    return null
}


fun updateSelectSchoolItemBean(schoolListItem: SchoolListItem){
    mmkv.encode("schoolItemBean",schoolListItem.toJsonStr())
}



/**
 * 获取搜索历史缓存数据
 */
fun getSearchHistoryData(): ArrayList<String> {
    val searchCacheStr = mmkv.decodeString("history")
    if (!TextUtils.isEmpty(searchCacheStr)) {
        return Gson().fromJson(searchCacheStr, object : TypeToken<ArrayList<String>>() {}.type)
    }
    return arrayListOf()
}

fun setSearchHistoryData(searchResponseStr: String) {
    mmkv.encode("history", searchResponseStr)
}


/**
 * 获取我的课程搜索搜索历史缓存数据
 */
fun getMyCourseSearchHistoryData(): ArrayList<String> {
    val searchCacheStr = mmkv.decodeString("my_course_history")
    if (!TextUtils.isEmpty(searchCacheStr)) {
        return Gson().fromJson(searchCacheStr, object : TypeToken<ArrayList<String>>() {}.type)
    }
    return arrayListOf()
}

fun setMyCourseSearchHistoryData(searchResponseStr: String) {
    mmkv.encode("my_course_history", searchResponseStr)
}


/**
 * 获取video搜索历史缓存数据
 */
fun getVideoSearchHistoryData(): ArrayList<String> {
    val searchCacheStr = mmkv.decodeString("video_history")
    if (!TextUtils.isEmpty(searchCacheStr)) {
        return Gson().fromJson(searchCacheStr, object : TypeToken<ArrayList<String>>() {}.type)
    }
    return arrayListOf()
}

fun setVideoSearchHistoryData(searchResponseStr: String) {
    mmkv.encode("video_history", searchResponseStr)
}


fun setLastWatchCourseId(courseId:String){
    mmkv.encode("LastWatchCourseId",courseId)
}

fun getLastWatchCourseId():String{
    return mmkv.decodeString("LastWatchCourseId","")?:""
}


fun setLastWatchChapterId(chapterId:String){
    mmkv.encode("LastWatchChapterId",chapterId)
}

fun getLastWatchChapterId():String{
    return mmkv.decodeString("LastWatchChapterId","")?:""
}


fun setLastWatchLessonId(lessonId:String){
    mmkv.encode("LastWatchLessonId",lessonId)
}

fun getLastWatchLessonId():String{
    return mmkv.decodeString("LastWatchLessonId","")?:""
}






/**
 * 推送是否打开
 */
fun setPushSwitch(isOpen:Boolean){
    mmkv.encode("isPushSwitchOpen",isOpen)
}

fun getPushSwitch():Boolean{
  return  mmkv.decodeBool("isPushSwitchOpen",true)
}

/**
 * 振动是否打开
 */
fun setOffVibrator(isOff:Boolean){
    mmkv.encode("isOffVibrator",isOff)
}

fun getIsOffVibrator():Boolean{
    return mmkv.decodeBool("isOffVibrator",false)
}



/**
 * 看课获取积分提示是否关闭
 */
fun setOffWatchLessonGetPointHint(isOff:Boolean){
    mmkv.encode("OffWatchLessonGetPointHint",isOff)
}

fun getIsOffWatchLessonGetPointHint():Boolean{
    return mmkv.decodeBool("OffWatchLessonGetPointHint",false)
}


/**
 * 线下学员权益说明按钮弹窗是否展示
 */

fun setIsShowVipRights(isShow: Boolean){
    mmkv.encode("IsShowVipRights",isShow)
}

fun getIsShowVipRights():Boolean{
    return mmkv.decodeBool("IsShowVipRights",true)
}



/**
 * 振动弹窗是否弹出过
 */

fun setHasShowedEnableVibrator(hasShowed: Boolean){
    mmkv.encode("setHasShowedEnableVibrator",hasShowed)
}

fun getHasShowedEnableVibrator():Boolean{
    return mmkv.decodeBool("setHasShowedEnableVibrator",false)
}



/**
 * 获取积分是否展示弹窗是否弹出过
 */

fun setHasShowedEnablePointGet(hasShowed: Boolean){
    mmkv.encode("HasShowedEnablePointGet",hasShowed)
}

fun getHasShowedEnablePointGet():Boolean{
    return mmkv.decodeBool("HasShowedEnablePointGet",false)
}


/**
 * 隐藏课程的手势布局是否显示过
 */
fun setIsHideCourseHandHintLayoutIsShowed(isShowed:Boolean){
    mmkv.encode("IsHideCourseHandHintLayoutIsShowed",isShowed)
}

fun getIsHideCourseHandHintLayoutIsShowed():Boolean{
    return mmkv.decodeBool("IsHideCourseHandHintLayoutIsShowed",false)
}


/**
 * 我的课程顶部的隐藏课程提示是否关闭了
 */
fun setIsMyCourseHideCourseHeadTitleHintIsClosed(isClosed:Boolean){
    mmkv.encode("IsMyCourseHideCourseHeadTitleHintIsClosed",isClosed)
}

fun getIsMyCourseHideCourseHeadTitleHintIsClosed():Boolean{
    return mmkv.decodeBool("IsMyCourseHideCourseHeadTitleHintIsClosed",false)
}


/**
 * 是否允许后台播放
 */
fun setIsEnableBackgroundPlay(isEnable:Boolean){
    mmkv.encode("IsEnableBackgroundPlay",isEnable)
}

fun getIsEnableBackgroundPlay():Boolean{
    return  mmkv.decodeBool("IsEnableBackgroundPlay",false)
}


/**
 * 是否显示过积分升级
 */
fun hasShowedPointUpdate():Boolean{
    return mmkv.decodeBool("hasShowedPointUpdate",false)
}


fun setHasShowedPointUpdate(hasShowed:Boolean){
    mmkv.encode("hasShowedPointUpdate",hasShowed)
}


/**
 * 首页展示的上次观看课程弹窗显示日期
 */
fun setMainCourseFragmentLastCourseAlertDate(date:String){
    mmkv.encode("MainCourseFragmentLastCourseAlertDate",date)
}

fun getMainCourseFragmentLastCourseAlertDate():String{
    return mmkv.decodeString("MainCourseFragmentLastCourseAlertDate")?:""
}


/**
 * 首页展示的弹窗显示日期
 */
fun setMainCourseDialogQueueAlertDate(date:String){
    mmkv.encode("MainCourseDialogQueueAlertDate",date)
}

fun getMainCourseDialogQueueAlertDate():String{
    return mmkv.decodeString("MainCourseDialogQueueAlertDate")?:""
}



/**
 * 互动消息页面展示的打卡学习群按钮
 */
fun setClockInLearningGroupCloseDate(date:String){
    mmkv.encode("ClockInLearningGroupCloseDate",date)
}

fun getClockInLearningGroupCloseDate():String{
    return mmkv.decodeString("ClockInLearningGroupCloseDate")?:""
}


/**
 * 截屏弹窗是否点击过关闭
 */
fun hasClosedScreenshotView():Boolean{
    return mmkv.decodeBool("hasClosedScreenshotView",false)
}

fun setHasClosedScreenshotView(hasClosed:Boolean){
    mmkv.encode("hasClosedScreenshotView",true)
}


/**
 * 涂鸦蒙层是否手动关闭过
 */
fun setHasClosedDoodleTouchHintMask(hasClosed:Boolean){
    mmkv.encode("hasClosedDoodleTouchHintMask",hasClosed)
}

fun hasClosedDoodleTouchHintMask():Boolean{
    return mmkv.decodeBool("hasClosedDoodleTouchHintMask",false)
}


/**
 * 笔记保存成功提醒是否设置了不再显示
 */
fun setNoteSaveSuccessDialogDoNotShowAgain(isSetNotShowAgain:Boolean){
    mmkv.encode("NoteSaveSuccessDialogDoNotShowAgain",isSetNotShowAgain)
}

fun isNoteSaveSuccessDialogDoNotShowAgain():Boolean{
    return mmkv.decodeBool("NoteSaveSuccessDialogDoNotShowAgain",false)
}


/**
 * 缓存地址类型
 */
const val DOWNLOAD_PATH_EXTERNAL_SDCARD = "119"
const val DOWNLOAD_PATH_PHONE = "121"
fun setDownloadPathType(downloadPathType:String){
    mmkv.encode("downloadPathType",downloadPathType)
}


fun getDownloadPathType():String{
    return mmkv.decodeString("downloadPathType", DOWNLOAD_PATH_PHONE)?: DOWNLOAD_PATH_PHONE
}








/**
 * 根据Key 删除
 */
fun MMKV.remove(@NonNull key: String) {
    mmkv.remove(key)
}

/**
 * 全部删除
 */
fun MMKV.clear() {
    mmkv.clearAll()
}







