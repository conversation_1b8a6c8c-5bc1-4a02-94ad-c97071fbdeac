package com.xtj.person.common.net.api

import com.blankj.utilcode.util.AppUtils
import com.xtj.person.common.net.api.params.DevMakeApkParams
import com.xtj.person.common.net.api.params.MakeApkParams
import rxhttp.wrapper.annotation.DefaultDomain


const val httpPrivacyPolicy = "https://m.xtjzx.cn/privacy/privacyAgreement.htm"
const val localPrivacyPolicy = "file:///android_asset/privacyAgreement.htm"
 val appConfigHead = "xtj-config/app/queryAppStatus?appCode=102&version=${AppUtils.getAppVersionName()}&storeName="
object NetUrl {

    // https://dev-api.xtjzx.cn/index



    // c3f68o0hon9npr9vfhmg
     val makeApkParams: MakeApkParams = DevMakeApkParams()

    fun getHostUrl():String{
        return  "https://dev-api.yxbstorage.com/"
    }




//    // 设置为默认域名-dev
 @DefaultDomain
 const val HOST_URL = "https://dev-api.xtjzx.cn/"




}