package com.xtj.person.common.net.repository

import android.net.Uri
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.common.base.appContext
import com.xtj.person.common.bean.ChangeStudentInfoResultBean
import com.xtj.person.common.bean.CheckListBean
import com.xtj.person.common.bean.CourseListBean
import com.xtj.person.common.bean.NewestCheckInResultBean
import com.xtj.person.common.bean.QueryUserInfoByPhoneBean
import com.xtj.person.common.bean.SchoolListBean
import com.xtj.person.common.ext.getLoginToken
import com.xtj.person.common.ext.getSelectSchoolItemBean
import com.xtj.person.common.net.api.NetUrl
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import rxhttp.toAwait
import rxhttp.wrapper.coroutines.Await
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.RxHttpBodyParam
import rxhttp.wrapper.param.RxHttpFormParam
import rxhttp.wrapper.param.RxHttpJsonArrayParam
import rxhttp.wrapper.param.RxHttpJsonParam
import rxhttp.wrapper.param.RxHttpNoBodyParam
import java.io.File


object DataRepository {




    fun RxHttpJsonParam.addHeaderAuthorization(): RxHttpJsonParam {
        addHeader(
            "AuthorizationTest",
            "Bearer ${getLoginToken()}"
        )
        addHeader("SCHOOLID",(getSelectSchoolItemBean()?.id?:-1).toString())

        return this
    }


    fun RxHttpNoBodyParam.addHeaderAuthorization(): RxHttpNoBodyParam {
        addHeader(
            "AuthorizationTest",
            "Bearer ${getLoginToken()}"
        )

        addHeader("SCHOOLID",(getSelectSchoolItemBean()?.id?:-1).toString())

        return this
    }


    fun RxHttpJsonArrayParam.addHeaderAuthorization(): RxHttpJsonArrayParam {
        addHeader(
            "AuthorizationTest",
            "Bearer ${getLoginToken()}"
        )

        addHeader("SCHOOLID",(getSelectSchoolItemBean()?.id?:-1).toString())

        return this
    }

    fun RxHttpFormParam.addHeaderAuthorization(): RxHttpFormParam {
        addHeader(
            "AuthorizationTest",
            "Bearer ${getLoginToken()}"
        )

        addHeader("SCHOOLID",(getSelectSchoolItemBean()?.id?:-1).toString())

        return this
    }

    fun RxHttpBodyParam.addHeaderAuthorization(): RxHttpBodyParam {
        addHeader(
            "AuthorizationTest",
            "Bearer ${getLoginToken()}"
        )

        addHeader("SCHOOLID",(getSelectSchoolItemBean()?.id?:-1).toString())

        return this
    }




    fun getSchoolList(page:Int,size:Int,keyword:String): Await<SchoolListBean>{
        return RxHttp.get(NetUrl.HOST_URL+"stars/api/app/school").add("page",page).add("size",size).add("keyword",keyword).addHeaderAuthorization().toAwait()
    }


    fun getSelectCourseList(keyword:String,size:Int,page:Int,date:String): Await<CourseListBean>{
        return RxHttp.get(NetUrl.HOST_URL+"stars/api/app/curriculum").add("page",page).add("size",size).add("keyword",keyword).add("date",date).addHeaderAuthorization().toAwait()
    }

    fun getCheckList(curriculumId:Int,date:String):Await<CheckListBean>{
        return RxHttp.get(NetUrl.HOST_URL+"stars/api/app/sign/curriculum/${curriculumId}?date=${date}").addHeaderAuthorization().toAwait()
    }


    fun queryUserInfoByPhone(phoneNum:String): Await<QueryUserInfoByPhoneBean>{
        return RxHttp.get(NetUrl.HOST_URL+"stars/api/app/student/${phoneNum}").addHeaderAuthorization().toAwait()
    }


    fun changeUserInfo(
        guid: String,
        userName: String,
        idNumber: String,
        phoneNum: String,
        iconPath: String
    ): Await<ChangeStudentInfoResultBean> {

        val builder = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("guid", guid)
            .addFormDataPart("username", userName)
            .addFormDataPart("id_number", idNumber)
            .addFormDataPart("phone_number", phoneNum)

        if (iconPath.isNotEmpty()) {
            val file = File(iconPath)
            builder.addFormDataPart(
                "icon",
                file.name,
                file.asRequestBody("image/jpeg".toMediaType())
            )
        }

        return RxHttp.postBody(NetUrl.HOST_URL + "stars/api/app/student").setBody(builder.build())
            .addHeaderAuthorization()
            .toAwait()
    }

    fun getNewestCheckInResult(curriculumId:Int,period:String): Await<NewestCheckInResultBean>{
        return RxHttp.get(NetUrl.HOST_URL+"stars/api/app/sign/now/${curriculumId}").add("period",period).addHeaderAuthorization().toAwait()

    }



}