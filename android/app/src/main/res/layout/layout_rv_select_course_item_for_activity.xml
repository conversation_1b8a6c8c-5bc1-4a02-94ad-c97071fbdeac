<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_marginTop="12.5dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/drawable_white_15"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_item_course_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="26国考笔试进阶A班"
            android:textSize="16dp"
            android:textColor="#000000"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="16dp"
            android:layout_marginStart="16.5dp"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1楼303教室"
            android:textSize="13dp"
            android:textColor="#909090"
            app:layout_constraintTop_toBottomOf="@id/tv_item_course_name"
            app:layout_constraintStart_toStartOf="@id/tv_item_course_name"
            android:layout_marginTop="15dp"
            android:id="@+id/tv_item_class_room_name"
            />

        <TextView
            android:layout_marginBottom="17dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:id="@+id/tv_item_during_date"
            android:layout_marginTop="15dp"
            app:layout_constraintStart_toStartOf="@id/tv_item_course_name"
            app:layout_constraintTop_toBottomOf="@id/tv_item_class_room_name"
            android:textColor="#898B97"
            android:textSize="13dp"
            android:text="2025.9.11-2025.10.11"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_to_check_in"
            android:paddingBottom="7.5dp"
            android:paddingTop="7.5dp"
            android:paddingEnd="15.5dp"
            android:paddingStart="15.5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="去考勤"
            android:textColor="@color/color_0054FF"
            android:textSize="13dp"
            android:background="@drawable/drawable_eef4ff_8"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            />



    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>