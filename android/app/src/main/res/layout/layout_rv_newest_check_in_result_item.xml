<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_marginTop="15dp"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="28dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/layout_check_item"
        android:background="@drawable/drawable_f7f8fc_10"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toTopOf="parent"
            app:round="8dp"
            android:id="@+id/iv_item_head_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="68dp"
            android:layout_height="68dp"/>

        <TextView
            android:layout_marginTop="4.5dp"
            android:id="@+id/tv_item_name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_item_head_bottom"
            android:textColor="#333333"
            android:textSize="11dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:layout_marginBottom="8.5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginTop="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_item_name"
            android:textSize="11dp"
            android:textColor="#0054FF"
            android:text="签到成功"
            android:id="@+id/tv_item_check_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>




</androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>