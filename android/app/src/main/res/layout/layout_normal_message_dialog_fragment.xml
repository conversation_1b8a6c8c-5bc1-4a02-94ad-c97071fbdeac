<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">


    <data>

        <variable
            name="click"
            type="com.xtj.person.dialog.NormalMessageDialogFragment.ProxyClick" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:background="@drawable/drawable_white_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/name_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="22dp"
                android:text="提示"
                android:textColor="@color/color_333333"
                android:textSize="17dp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:layout_marginLeft="20dp"
                android:gravity="center"
                android:layout_marginRight="20dp"
                android:textColor="@color/color_333333"
                android:textSize="15dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/name_tip" />


            <TextView
                android:layout_width="120dp"
                android:layout_height="40dp"
                android:layout_marginStart="19dp"
                android:layout_marginBottom="21dp"
                android:layout_marginTop="25dp"
                android:background="@drawable/drawable_f6_bg"
                android:gravity="center"
                android:onClick="@{()->click.dismissBtn()}"
                android:text="取消"
                android:textColor="@color/color_424242"
                android:textSize="14dp"
                app:layout_constraintTop_toBottomOf="@id/tv_message"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />


            <TextView
                android:layout_width="120dp"
                android:layout_height="40dp"
                android:layout_marginEnd="19dp"
                android:layout_marginBottom="21dp"
                android:background="@drawable/drawable_0054_20_bg"
                android:gravity="center"
                android:onClick="@{()->click.clickSure()}"
                android:layout_marginTop="25dp"
                android:text="确认"
                android:textColor="@color/color_f9f9f9"
                android:textSize="14dp"
                app:layout_constraintTop_toBottomOf="@id/tv_message"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>