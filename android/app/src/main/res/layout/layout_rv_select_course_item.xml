<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_root"
        android:layout_marginTop="14dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="60dp">

        <TextView
            android:layout_marginTop="16dp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="河南校区"
            android:textSize="15dp"
            android:textColor="@color/color_333333"
            app:layout_constraintTop_toTopOf="parent"
            android:id="@+id/tv_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <ImageView
            android:visibility="gone"
            android:id="@+id/iv_select_status"
            android:layout_marginEnd="6dp"
            app:layout_constraintBottom_toBottomOf="@id/tv_name"
            app:layout_constraintTop_toTopOf="@id/tv_name"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@drawable/icon_select_course_dialog_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <View
            android:layout_marginTop="15.5dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_name"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="#979797"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>