<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/drawable_white_15"
        android:id="@+id/layout_check_list_item"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="20dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_time_bg"
            app:layout_constraintTop_toTopOf="parent"
            android:scaleType="centerCrop"
            android:src="@drawable/bg_check_list_item_morning"
            android:layout_width="match_parent"
            android:layout_height="41dp"/>

        <TextView
            android:id="@+id/tv_item_time"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_time_bg"
            app:layout_constraintBottom_toBottomOf="@id/iv_time_bg"
            android:textStyle="bold"
            android:layout_marginStart="16.5dp"
            android:textColor="#C68306"
            android:textSize="13dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_item_start_check_in"
            android:layout_marginEnd="16dp"
            app:layout_constraintBottom_toBottomOf="@id/iv_time_bg"
            app:layout_constraintTop_toTopOf="@id/iv_time_bg"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            android:textColor="#C68306"
            android:textSize="13dp"
            android:text="开始考勤"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>


        <TextView
            android:id="@+id/tv_item_course_name"
            android:layout_marginTop="5dp"
            android:layout_marginStart="16.5dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_time_bg"
            android:textStyle="bold"
            android:textColor="#000000"
            android:textSize="16dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:layout_marginTop="15dp"
            android:layout_marginStart="16.5dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_item_course_name"
            android:textColor="#898B97"
            android:textSize="13dp"
            android:visibility="gone"
            android:id="@+id/tv_item_lesson_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>


        <TextView
            android:id="@+id/tv_item_class_room"
            android:layout_marginTop="15dp"
            android:layout_marginStart="16.5dp"
            android:layout_marginBottom="17dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_item_lesson_name"
            android:textColor="#909090"
            android:textSize="13dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:layout_marginBottom="16dp"
            android:layout_marginEnd="16dp"
            android:id="@+id/tv_item_check_in_num"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:textSize="13dp"
            android:textColor="@color/color_0054FF"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:layout_marginEnd="5dp"
            android:id="@+id/tv_item_check_in_num_title"
            android:textColor="#898B97"
            android:textSize="13dp"
            android:text="签到人数:"
            app:layout_constraintBottom_toBottomOf="@id/tv_item_check_in_num"
            app:layout_constraintTop_toTopOf="@id/tv_item_check_in_num"
            app:layout_constraintEnd_toStartOf="@id/tv_item_check_in_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>




    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>