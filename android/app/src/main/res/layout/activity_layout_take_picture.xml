<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_F7F8FC">
    <include
        android:id="@+id/include"
        layout="@layout/layout_common_title" />



    <androidx.camera.view.PreviewView
        android:id="@+id/preview"
        android:layout_width="match_parent"
        app:layout_constraintTop_toBottomOf="@id/include"
        app:layout_constraintBottom_toTopOf="@id/layout_bottom"
        android:layout_height="0dp"/>

    <com.xtj.person.OverlayView
        android:id="@+id/overlay"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toTopOf="@id/layout_bottom"
        app:layout_constraintTop_toBottomOf="@id/include"
        android:layout_height="0dp"
        />



    
    <ImageView
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toTopOf="@id/layout_bottom"
        app:layout_constraintTop_toBottomOf="@id/include"
        android:layout_width="match_parent"
        android:src="@drawable/icon_camera_mask"
        android:layout_height="0dp"/>


    



    <TextView
        android:visibility="gone"
        android:id="@+id/tv_no_face_detect"
        android:layout_marginTop="28.5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_check_status_top"
        android:textColor="@color/white"
        android:textSize="13dp"
        android:text="未检测到人脸"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    
    
    <ImageView
        android:id="@+id/iv_start_shutter"
        android:layout_marginBottom="13.5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/layout_bottom"
        android:src="@drawable/icon_camera_shutter"
        android:layout_width="54.5dp"
        android:layout_height="54.5dp"/>


    <ImageView
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:src="@drawable/icon_switch_camera"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_start_shutter"
        app:layout_constraintBottom_toBottomOf="@id/iv_start_shutter"
        android:layout_marginEnd="16dp"
        android:id="@+id/iv_switch_camera"
        />




    <ImageView
        android:clickable="true"
        android:visibility="gone"
        android:scaleType="centerCrop"
        android:id="@+id/iv_take_picture_result"
        app:layout_constraintBottom_toTopOf="@id/layout_bottom"
        app:layout_constraintTop_toBottomOf="@id/include"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>

    <RelativeLayout
        android:id="@+id/layout_check_status_top"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/drawable_0054ff_10"
        android:weightSum="2"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/include"
        android:layout_width="match_parent"
        android:layout_height="45dp">

        <LinearLayout
            android:layout_marginStart="14dp"
            android:layout_centerVertical="true"
            android:layout_alignParentLeft="true"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <View
                android:layout_marginStart="2dp"
                android:layout_width="4dp"
                android:layout_height="8dp"
                android:background="@drawable/drawable_white_15"
                android:rotation="30"
                />
            <View
                android:layout_marginStart="3dp"
                android:layout_width="4dp"
                android:layout_height="8dp"
                android:background="@drawable/drawable_white_15"
                android:rotation="30"
                android:layout_marginEnd="2dp"
                />
        </LinearLayout>



        <TextView
            android:layout_centerInParent="true"
            android:layout_marginStart="27dp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:textSize="13dp"
            android:text="脸部与摄像头平视，识别中"
            android:id="@+id/tv_top_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>



        <LinearLayout
            android:layout_marginEnd="14dp"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <View
                android:layout_marginStart="2dp"
                android:layout_width="4dp"
                android:layout_height="8dp"
                android:background="@drawable/drawable_white_15"
                android:rotation="30"
                />
            <View
                android:layout_marginStart="3dp"
                android:layout_width="4dp"
                android:layout_height="8dp"
                android:background="@drawable/drawable_white_15"
                android:rotation="30"
                android:layout_marginEnd="2dp"
                />
        </LinearLayout>

    </RelativeLayout>



    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_bottom"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:visibility="visible"
            android:layout_marginBottom="27dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginStart="16dp"
            android:id="@+id/ll_bottom_hint"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:weightSum="3"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:gravity="center"
                android:background="@drawable/drawable_f7f8fc_10"
                android:layout_height="wrap_content">

                <ImageView
                    android:adjustViewBounds="true"
                    android:src="@drawable/icon_hint_no_glasses"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:layout_marginBottom="12dp"
                    android:layout_marginTop="6dp"
                    android:textSize="12dp"
                    android:text="不要眼镜、帽子"
                    android:textColor="#898B97"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </LinearLayout>


            <LinearLayout
                android:layout_marginEnd="12.5dp"
                android:layout_marginStart="12.5dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:gravity="center"
                android:background="@drawable/drawable_f7f8fc_10"
                android:layout_height="wrap_content">

                <ImageView
                    android:adjustViewBounds="true"
                    android:src="@drawable/icon_hint_no_multi_people"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:layout_marginBottom="12dp"
                    android:layout_marginTop="6dp"
                    android:textSize="12dp"
                    android:text="不要多人"
                    android:textColor="#898B97"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </LinearLayout>

            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:gravity="center"
                android:background="@drawable/drawable_f7f8fc_10"
                android:layout_height="wrap_content">

                <ImageView
                    android:adjustViewBounds="true"
                    android:src="@drawable/icon_no_dark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:layout_marginBottom="12dp"
                    android:layout_marginTop="6dp"
                    android:textSize="12dp"
                    android:text="不要光线暗"
                    android:textColor="#898B97"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </LinearLayout>




        </LinearLayout>


        <LinearLayout
            android:visibility="gone"
            android:layout_marginTop="15.5dp"
            android:layout_marginBottom="24dp"
            android:layout_marginEnd="16dp"
            android:layout_marginStart="16dp"
            android:id="@+id/ll_bottom_action_hint"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:weightSum="2"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/ll_take_picture_again"
                android:layout_marginEnd="7.5dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:gravity="center"
                android:background="@drawable/drawable_f7f8fc_10"
                android:layout_height="wrap_content">

                <ImageView
                    android:layout_marginTop="25.5dp"
                    android:adjustViewBounds="true"
                    android:src="@drawable/icon_take_picture_again"
                    android:layout_width="35dp"
                    android:layout_height="35dp"/>

                <TextView
                    android:layout_marginBottom="25dp"
                    android:layout_marginTop="6dp"
                    android:textSize="16dp"
                    android:text="重拍"
                    android:textColor="#343434"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_commint"
                android:layout_marginStart="7.5dp"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_width="0dp"
                android:gravity="center"
                android:background="@drawable/drawable_f7f8fc_10"
                android:layout_height="wrap_content">

                <ImageView
                    android:layout_marginTop="25.5dp"
                    android:adjustViewBounds="true"
                    android:src="@drawable/icon_commit_picture"
                    android:layout_width="35dp"
                    android:layout_height="35dp"/>

                <TextView
                    android:layout_marginBottom="25dp"
                    android:layout_marginTop="6dp"
                    android:textSize="16dp"
                    android:text="提交"
                    android:textColor="#343434"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </LinearLayout>



        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>







</androidx.constraintlayout.widget.ConstraintLayout>